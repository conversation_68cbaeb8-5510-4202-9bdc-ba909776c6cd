const io = require("socket.io-client");

// Configuration
const SERVER_URL = "http://localhost:3006";
const ADMIN_JWT =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6ImRjNzcwNDJjLTgwMDUtNGU0Yi05YTQ3LWM3YmFiMzk5ZGM4ZiIsInBob25lIjoiODg4ODg4ODg4OCIsImlhdCI6MTc1MTIyNzUxMywiZXhwIjoxNzgyNzYzNTEzfQ.nK7wseqypO4HgmH6r5FexLlJHppJrGyqa5SmDYGWECQ";
const USER1_JWT =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6ImJmZTUwOTZmLTlhZDItNDBhZS1hZWY0LWQzOGQ5NjI1YjZkMCIsInBob25lIjoiOTg3NjU0MzIxMCIsImlhdCI6MTc1MTIyMjg2OSwiZXhwIjoxNzgyNzU4ODY5fQ.OtdndjtNqjQ4-JfaUd3DzIFOubyGiFGBdqkBzAOkJRY";
const USER2_JWT =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6ImM1NzQ4NmZiLTAwNzItNDQzMi1iNTgyLWNiMmJkZDMzZjdhOCIsInBob25lIjoiOTk5OTk5OTk5OSIsImlhdCI6MTc1MTIyNDE4MywiZXhwIjoxNzgyNzYwMTgzfQ.YourJWTTokenHere";

// Test state
let testResults = {
  passed: 0,
  failed: 0,
  errors: [],
};

let auctionId = null;
let playerId = null;
let currentBid = 0;

// Helper function to log test results
function logTest(testName, success, data = null, error = null) {
  if (success) {
    console.log(`✅ ${testName} - PASSED`);
    testResults.passed++;
    if (data) console.log("   Response:", JSON.stringify(data, null, 2));
  } else {
    console.log(`❌ ${testName} - FAILED`);
    testResults.failed++;
    if (error) {
      console.log("   Error:", error);
      testResults.errors.push({ test: testName, error });
    }
  }
  console.log("");
}

// Helper function to wait
function wait(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

// Helper function to run test with timeout
function runTestWithTimeout(testPromise, timeoutMs = 10000) {
  return Promise.race([
    testPromise,
    new Promise((_, reject) =>
      setTimeout(() => reject(new Error("Test timeout")), timeoutMs)
    ),
  ]);
}

// Test function
async function runTests() {
  console.log("🚀 Starting Comprehensive Auction Flow Tests...\n");

  const socket = io(SERVER_URL);

  // Wait for connection with timeout
  try {
    await runTestWithTimeout(
      new Promise((resolve, reject) => {
        socket.on("connect", () => {
          console.log("🔌 Connected to server");
          resolve();
        });

        socket.on("connect_error", (error) => {
          console.log("❌ Connection failed:", error.message);
          reject(error);
        });

        // Timeout for connection
        setTimeout(() => reject(new Error("Connection timeout")), 5000);
      })
    );
  } catch (error) {
    console.log("❌ Failed to connect to server:", error.message);
    process.exit(1);
  }

  // Set up event listeners
  socket.on("error", (data) => {
    console.log("❌ Socket Error:", data);
  });

  // Test 1: Admin Registration (No auctionId required)
  console.log("📋 Test 1: Admin Registration");
  try {
    await runTestWithTimeout(
      new Promise((resolve) => {
        socket.emit("registerAdmin", {
          jwt: ADMIN_JWT,
          action: "registerAdmin",
        });

        socket.once("registerAdmin", (data) => {
          if (data.status) {
            logTest("Admin Registration", true, data);
          } else {
            logTest("Admin Registration", false, null, data.message);
          }
          resolve();
        });
      })
    );
  } catch (error) {
    logTest("Admin Registration", false, null, error.message);
  }

  // Test 2: Create Auction
  console.log("📋 Test 2: Create Auction");
  try {
    await runTestWithTimeout(
      new Promise((resolve) => {
        socket.emit("createAuction", {
          action: "createAuction",
          jwt: ADMIN_JWT,
          name: "IPL 2024 Test Auction",
          description: "Comprehensive test auction for IPL 2024",
          startDate: "2024-01-15T10:00:00Z",
          endDate: "2024-01-15T18:00:00Z",
        });

        socket.once("createAuction", (data) => {
          if (data.status && data.auction) {
            auctionId = data.auction.id;
            logTest("Create Auction", true, data);
          } else {
            logTest(
              "Create Auction",
              false,
              null,
              data.message || "Failed to create auction"
            );
          }
          resolve();
        });
      })
    );
  } catch (error) {
    logTest("Create Auction", false, null, error.message);
  }

  if (!auctionId) {
    console.log("❌ Cannot continue without auctionId");
    socket.disconnect();
    return;
  }

  // Test 3: Get All Auctions
  console.log("📋 Test 3: Get All Auctions");
  try {
    await runTestWithTimeout(
      new Promise((resolve) => {
        socket.emit("getAuctions", {
          action: "getAuctions",
          jwt: ADMIN_JWT,
        });

        socket.once("getAuctions", (data) => {
          if (data.status && Array.isArray(data.auctions)) {
            logTest("Get Auctions", true, { count: data.auctions.length });
          } else {
            logTest("Get Auctions", false, null, "Failed to get auctions");
          }
          resolve();
        });
      })
    );
  } catch (error) {
    logTest("Get Auctions", false, null, error.message);
  }

  // Test 4: Join Auction (Admin)
  console.log("📋 Test 4: Join Auction (Admin)");
  try {
    await runTestWithTimeout(
      new Promise((resolve) => {
        socket.emit("joinAuction", {
          action: "joinAuction",
          jwt: ADMIN_JWT,
          auctionId: auctionId,
        });

        socket.once("joinAuction", (data) => {
          if (data.status) {
            logTest("Join Auction", true, data);
          } else {
            logTest("Join Auction", false, null, data.message);
          }
          resolve();
        });
      })
    );
  } catch (error) {
    logTest("Join Auction", false, null, error.message);
  }

  // Test 5: Start Auction
  console.log("📋 Test 5: Start Auction");
  try {
    await runTestWithTimeout(
      new Promise((resolve) => {
        socket.emit("startAuction", {
          action: "startAuction",
          jwt: ADMIN_JWT,
          auctionId: auctionId,
          duration: 60,
          averagePlayerCost: 100000,
          batter: 2,
          bowler: 2,
          wicketKeeper: 1,
          batterMax: 2,
          bowlerMax: 2,
          wicketKeeperMax: 1,
          amount: 1000000,
        });

        socket.once("startAuction", (data) => {
          if (data.status) {
            logTest("Start Auction", true, data);
          } else {
            logTest("Start Auction", false, null, data.message);
          }
          resolve();
        });
      })
    );
  } catch (error) {
    logTest("Start Auction", false, null, error.message);
  }

  // Wait a bit for auction to initialize
  await wait(2000);

  // Test 6: Get New Player
  console.log("📋 Test 6: Get New Player");
  try {
    await runTestWithTimeout(
      new Promise((resolve) => {
        socket.emit("getNewPlayer", {
          action: "getNewPlayer",
          jwt: ADMIN_JWT,
          auctionId: auctionId,
        });

        socket.once("getNewPlayer", (data) => {
          if (data.status && data.player) {
            playerId = data.player.id;
            logTest("Get New Player", true, {
              playerId: data.player.id,
              name: data.player.name,
            });
          } else {
            logTest("Get New Player", false, null, data.message);
          }
          resolve();
        });
      })
    );
  } catch (error) {
    logTest("Get New Player", false, null, error.message);
  }

  if (!playerId) {
    console.log("❌ Cannot continue without playerId");
    socket.disconnect();
    return;
  }

  // Test 7: Add First Bid
  console.log("📋 Test 7: Add First Bid");
  currentBid = 50000;
  try {
    await runTestWithTimeout(
      new Promise((resolve) => {
        socket.emit("addBid", {
          action: "addBid",
          jwt: ADMIN_JWT,
          auctionId: auctionId,
          playerId: playerId,
          bid: currentBid,
        });

        socket.once("addBid", (data) => {
          if (data.status) {
            logTest("Add First Bid", true, data);
          } else {
            logTest("Add First Bid", false, null, data.message);
          }
          resolve();
        });
      })
    );
  } catch (error) {
    logTest("Add First Bid", false, null, error.message);
  }

  // Test 8: Get Auction Status
  console.log("📋 Test 8: Get Auction Status");
  try {
    await runTestWithTimeout(
      new Promise((resolve) => {
        socket.emit("auctionStatus", {
          action: "auctionStatus",
          jwt: ADMIN_JWT,
          auctionId: auctionId,
        });

        socket.once("auctionStatus", (data) => {
          if (data.status) {
            logTest("Get Auction Status", true, data);
          } else {
            logTest("Get Auction Status", false, null, data.message);
          }
          resolve();
        });
      })
    );
  } catch (error) {
    logTest("Get Auction Status", false, null, error.message);
  }

  // Test 9: Get Live Bidders
  console.log("📋 Test 9: Get Live Bidders");
  try {
    await runTestWithTimeout(
      new Promise((resolve) => {
        socket.emit("liveBidders", {
          action: "liveBidders",
          jwt: ADMIN_JWT,
          auctionId: auctionId,
        });

        socket.once("liveBidders", (data) => {
          if (data.status) {
            logTest("Get Live Bidders", true, data);
          } else {
            logTest("Get Live Bidders", false, null, data.message);
          }
          resolve();
        });
      })
    );
  } catch (error) {
    logTest("Get Live Bidders", false, null, error.message);
  }

  // Test 10: Add Higher Bid
  console.log("📋 Test 10: Add Higher Bid");
  currentBid = 75000;
  try {
    await runTestWithTimeout(
      new Promise((resolve) => {
        socket.emit("addBid", {
          action: "addBid",
          jwt: ADMIN_JWT,
          auctionId: auctionId,
          playerId: playerId,
          bid: currentBid,
        });

        socket.once("addBid", (data) => {
          if (data.status) {
            logTest("Add Higher Bid", true, data);
          } else {
            logTest("Add Higher Bid", false, null, data.message);
          }
          resolve();
        });
      })
    );
  } catch (error) {
    logTest("Add Higher Bid", false, null, error.message);
  }

  // Test 11: Get Upcoming Players
  console.log("📋 Test 11: Get Upcoming Players");
  try {
    await runTestWithTimeout(
      new Promise((resolve) => {
        socket.emit("getUpcomingPlayers", {
          action: "getUpcomingPlayers",
          jwt: ADMIN_JWT,
          auctionId: auctionId,
        });

        socket.once("getUpcomingPlayers", (data) => {
          if (data.status) {
            logTest("Get Upcoming Players", true, data);
          } else {
            logTest("Get Upcoming Players", false, null, data.message);
          }
          resolve();
        });
      })
    );
  } catch (error) {
    logTest("Get Upcoming Players", false, null, error.message);
  }

  // Test 12: Last Call
  console.log("📋 Test 12: Last Call");
  try {
    await runTestWithTimeout(
      new Promise((resolve) => {
        socket.emit("lastCall", {
          action: "lastCall",
          jwt: ADMIN_JWT,
          auctionId: auctionId,
          playerId: playerId,
        });

        socket.once("lastCall", (data) => {
          if (data.status) {
            logTest("Last Call", true, data);
          } else {
            logTest("Last Call", false, null, data.message);
          }
          resolve();
        });
      })
    );
  } catch (error) {
    logTest("Last Call", false, null, error.message);
  }

  // Wait for last call to complete
  await wait(3000);

  // Test 13: Add Final Bid
  console.log("📋 Test 13: Add Final Bid");
  currentBid = 100000;
  try {
    await runTestWithTimeout(
      new Promise((resolve) => {
        socket.emit("addBid", {
          action: "addBid",
          jwt: ADMIN_JWT,
          auctionId: auctionId,
          playerId: playerId,
          bid: currentBid,
        });

        socket.once("addBid", (data) => {
          if (data.status) {
            logTest("Add Final Bid", true, data);
          } else {
            logTest("Add Final Bid", false, null, data.message);
          }
          resolve();
        });
      })
    );
  } catch (error) {
    logTest("Add Final Bid", false, null, error.message);
  }

  // Test 14: Get All User Details
  console.log("📋 Test 14: Get All User Details");
  try {
    await runTestWithTimeout(
      new Promise((resolve) => {
        socket.emit("getAllUserDetails", {
          action: "getAllUserDetails",
          jwt: ADMIN_JWT,
          auctionId: auctionId,
        });

        socket.once("getAllUserDetails", (data) => {
          if (data.status) {
            logTest("Get All User Details", true, data);
          } else {
            logTest("Get All User Details", false, null, data.message);
          }
          resolve();
        });
      })
    );
  } catch (error) {
    logTest("Get All User Details", false, null, error.message);
  }

  // Test 15: Auction Completed
  console.log("📋 Test 15: Auction Completed");
  try {
    await runTestWithTimeout(
      new Promise((resolve) => {
        socket.emit("auctionCompleted", {
          action: "auctionCompleted",
          jwt: ADMIN_JWT,
          auctionId: auctionId,
          playerId: playerId,
        });

        socket.once("auctionCompleted", (data) => {
          if (data.status) {
            logTest("Auction Completed", true, data);
          } else {
            logTest("Auction Completed", false, null, data.message);
          }
          resolve();
        });
      })
    );
  } catch (error) {
    logTest("Auction Completed", false, null, error.message);
  }

  // Test 16: Get Auction Details
  console.log("📋 Test 16: Get Auction Details");
  try {
    await runTestWithTimeout(
      new Promise((resolve) => {
        socket.emit("getAuctionDetails", {
          action: "getAuctionDetails",
          jwt: ADMIN_JWT,
          id: auctionId,
        });

        socket.once("getAuctionDetails", (data) => {
          if (data.status) {
            logTest("Get Auction Details", true, data);
          } else {
            logTest("Get Auction Details", false, null, data.message);
          }
          resolve();
        });
      })
    );
  } catch (error) {
    logTest("Get Auction Details", false, null, error.message);
  }

  // Test 17: Update Auction
  console.log("📋 Test 17: Update Auction");
  try {
    await runTestWithTimeout(
      new Promise((resolve) => {
        socket.emit("updateAuction", {
          action: "updateAuction",
          jwt: ADMIN_JWT,
          id: auctionId,
          data: {
            status: "completed",
            description: "Updated description after completion",
          },
        });

        socket.once("updateAuction", (data) => {
          if (data.status) {
            logTest("Update Auction", true, data);
          } else {
            logTest("Update Auction", false, null, data.message);
          }
          resolve();
        });
      })
    );
  } catch (error) {
    logTest("Update Auction", false, null, error.message);
  }

  // Test 18: System Health
  console.log("📋 Test 18: System Health");
  try {
    await runTestWithTimeout(
      new Promise((resolve) => {
        socket.emit("systemHealth", {
          action: "systemHealth",
        });

        socket.once("systemHealth", (data) => {
          if (data.status) {
            logTest("System Health", true, data);
          } else {
            logTest("System Health", false, null, data.message);
          }
          resolve();
        });
      })
    );
  } catch (error) {
    logTest("System Health", false, null, error.message);
  }

  // Test 19: Create Second Auction (Multi-auction test)
  console.log("📋 Test 19: Create Second Auction (Multi-auction test)");
  try {
    await runTestWithTimeout(
      new Promise((resolve) => {
        socket.emit("createAuction", {
          action: "createAuction",
          jwt: ADMIN_JWT,
          name: "Second Test Auction",
          description: "Testing multi-auction isolation",
          startDate: "2024-01-16T10:00:00Z",
          endDate: "2024-01-16T18:00:00Z",
        });

        socket.once("createAuction", (data) => {
          if (data.status && data.auction) {
            logTest("Create Second Auction", true, {
              auctionId: data.auction.id,
            });
          } else {
            logTest("Create Second Auction", false, null, data.message);
          }
          resolve();
        });
      })
    );
  } catch (error) {
    logTest("Create Second Auction", false, null, error.message);
  }

  // Test 20: Error Testing - Invalid auctionId
  console.log("📋 Test 20: Error Testing - Invalid auctionId");
  try {
    await runTestWithTimeout(
      new Promise((resolve) => {
        socket.emit("registerUser", {
          action: "registerUser",
          jwt: ADMIN_JWT,
          auctionId: "invalid-auction-id",
        });

        socket.once("error", (data) => {
          if (data.message && data.message.includes("auctionId")) {
            logTest("Error Testing - Invalid auctionId", true, data);
          } else {
            logTest(
              "Error Testing - Invalid auctionId",
              false,
              null,
              "Expected error not received"
            );
          }
          resolve();
        });
      })
    );
  } catch (error) {
    logTest("Error Testing - Invalid auctionId", false, null, error.message);
  }

  // Test 21: Error Testing - Missing auctionId
  console.log("📋 Test 21: Error Testing - Missing auctionId");
  try {
    await runTestWithTimeout(
      new Promise((resolve) => {
        socket.emit("registerUser", {
          action: "registerUser",
          jwt: ADMIN_JWT,
        });

        socket.once("error", (data) => {
          if (data.message && data.message.includes("auctionId")) {
            logTest("Error Testing - Missing auctionId", true, data);
          } else {
            logTest(
              "Error Testing - Missing auctionId",
              false,
              null,
              "Expected error not received"
            );
          }
          resolve();
        });
      })
    );
  } catch (error) {
    logTest("Error Testing - Missing auctionId", false, null, error.message);
  }

  // Test 22: Disconnect User
  console.log("📋 Test 22: Disconnect User");
  try {
    await runTestWithTimeout(
      new Promise((resolve) => {
        socket.emit("disconnectUser", {
          action: "disconnectUser",
          jwt: ADMIN_JWT,
          auctionId: auctionId,
        });

        socket.once("disconnectUser", (data) => {
          if (data.status) {
            logTest("Disconnect User", true, data);
          } else {
            logTest("Disconnect User", false, null, data.message);
          }
          resolve();
        });
      })
    );
  } catch (error) {
    logTest("Disconnect User", false, null, error.message);
  }

  // Final summary
  console.log("📊 Test Summary:");
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(
    `📈 Success Rate: ${(
      (testResults.passed / (testResults.passed + testResults.failed)) *
      100
    ).toFixed(2)}%`
  );

  if (testResults.errors.length > 0) {
    console.log("\n❌ Errors:");
    testResults.errors.forEach((error) => {
      console.log(`   - ${error.test}: ${error.error}`);
    });
  }

  // Disconnect
  socket.disconnect();
  console.log("\n🔌 Disconnected from server");
  process.exit(0);
}

// Run the tests
runTests().catch((error) => {
  console.error("❌ Test suite failed:", error);
  process.exit(1);
});

async function testMultiUserRegistration() {
  console.log("🧪 Testing Multi-User Registration...\n");

  // Create admin socket
  const adminSocket = io(SERVER_URL);

  // Create user sockets
  const user1Socket = io(SERVER_URL);
  const user2Socket = io(SERVER_URL);

  try {
    // Wait for connections
    await new Promise((resolve) => setTimeout(resolve, 1000));

    console.log("📡 Testing Admin Registration...");

    // Register admin
    adminSocket.emit("registerAdmin", {
      action: "registerAdmin",
      jwt: ADMIN_JWT,
    });

    await new Promise((resolve) => {
      adminSocket.once("registerAdmin", (data) => {
        console.log("✅ Admin registered:", data.status);
        resolve();
      });
    });

    console.log("📡 Testing Auction Creation...");

    // Create auction
    adminSocket.emit("createAuction", {
      action: "createAuction",
      jwt: ADMIN_JWT,
      data: {
        name: "Test Auction",
        description: "Test auction for multi-user testing",
        startDate: new Date().toISOString(),
        endDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        max_participants: 10,
      },
    });

    let auctionId;
    await new Promise((resolve) => {
      adminSocket.once("createAuction", (data) => {
        console.log("✅ Auction created:", data.status);
        auctionId = data.auction.id;
        resolve();
      });
    });

    console.log("📡 Testing User 1 Registration...");

    // Register user 1
    user1Socket.emit("registerUser", {
      action: "registerUser",
      jwt: USER1_JWT,
      auctionId: auctionId,
    });

    await new Promise((resolve) => {
      user1Socket.once("registerUser", (data) => {
        console.log("✅ User 1 registered:", data.status);
        resolve();
      });
    });

    console.log("📡 Testing User 2 Registration...");

    // Register user 2
    user2Socket.emit("registerUser", {
      action: "registerUser",
      jwt: USER2_JWT,
      auctionId: auctionId,
    });

    await new Promise((resolve) => {
      user2Socket.once("registerUser", (data) => {
        console.log("✅ User 2 registered:", data.status);
        resolve();
      });
    });

    console.log("📡 Testing Join Auction...");

    // Test join auction for both users
    user1Socket.emit("joinAuction", {
      action: "joinAuction",
      jwt: USER1_JWT,
      auctionId: auctionId,
    });

    await new Promise((resolve) => {
      user1Socket.once("joinAuction", (data) => {
        console.log("✅ User 1 joined auction:", data.status);
        resolve();
      });
    });

    user2Socket.emit("joinAuction", {
      action: "joinAuction",
      jwt: USER2_JWT,
      auctionId: auctionId,
    });

    await new Promise((resolve) => {
      user2Socket.once("joinAuction", (data) => {
        console.log("✅ User 2 joined auction:", data.status);
        resolve();
      });
    });

    console.log("📡 Testing Get Auctions...");

    // Test get auctions
    user1Socket.emit("getAuctions", {
      action: "getAuctions",
      jwt: USER1_JWT,
    });

    await new Promise((resolve) => {
      user1Socket.once("getAuctions", (data) => {
        console.log("✅ Get auctions successful:", data.status);
        console.log("📊 Auctions count:", data.auctions?.length || 0);
        resolve();
      });
    });

    console.log("\n🎉 All tests completed successfully!");
    console.log("✅ Multiple users can register without replacement");
    console.log("✅ Admin sockets are found and working");
    console.log("✅ Join auction works without crashing");
  } catch (error) {
    console.error("❌ Test failed:", error);
  } finally {
    // Cleanup
    adminSocket.disconnect();
    user1Socket.disconnect();
    user2Socket.disconnect();
    process.exit(0);
  }
}

// Run the test
testMultiUserRegistration();
