const io = require("socket.io-client");

const SERVER_URL = "http://localhost:3006";
const USER1_JWT =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6ImJmZTUwOTZmLTlhZDItNDBhZS1hZWY0LWQzOGQ5NjI1YjZkMCIsInBob25lIjoiOTg3NjU0MzIxMCIsImlhdCI6MTc1MTIyMjg2OSwiZXhwIjoxNzgyNzU4ODY5fQ.OtdndjtNqjQ4-JfaUd3DzIFOubyGiFGBdqkBzAOkJRY";
const USER2_JWT =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6ImM1NzQ4NmZiLTAwNzItNDQzMi1iNTgyLWNiMmJkZDMzZjdhOCIsInBob25lIjoiOTk5OTk5OTk5OSIsImlhdCI6MTc1MTIyNDE4MywiZXhwIjoxNzgyNzYwMTgzfQ.YourJWTTokenHere";

async function testSocketConnections() {
  console.log("🧪 Testing Socket Connection Uniqueness...\n");

  // Create separate socket connections for each user
  console.log("📡 Creating socket connections...");
  const socket1 = io(SERVER_URL);
  const socket2 = io(SERVER_URL);

  try {
    // Wait for connections to establish
    await new Promise((resolve) => setTimeout(resolve, 2000));

    console.log("📡 Socket IDs after connection:");
    console.log("Socket 1 ID:", socket1.id);
    console.log("Socket 2 ID:", socket2.id);

    // Check if IDs are unique
    if (socket1.id !== socket2.id) {
      console.log("✅ Socket IDs are unique!");
    } else {
      console.log("❌ Socket IDs are the same!");
      console.log("This indicates the client is reusing the same connection.");
    }

    // Test user registration
    console.log("\n📡 Testing User Registration...");

    // Register user 1
    socket1.emit("registerUser", {
      action: "registerUser",
      jwt: USER1_JWT,
      auctionId: "test-auction-456",
    });

    // Register user 2
    socket2.emit("registerUser", {
      action: "registerUser",
      jwt: USER2_JWT,
      auctionId: "test-auction-456",
    });

    let user1Response = null;
    let user2Response = null;

    socket1.once("registerUser", (data) => {
      console.log("✅ User 1 registered with socket ID:", socket1.id);
      user1Response = data;
    });

    socket2.once("registerUser", (data) => {
      console.log("✅ User 2 registered with socket ID:", socket2.id);
      user2Response = data;
    });

    // Wait for responses
    await new Promise((resolve) => setTimeout(resolve, 3000));

    if (user1Response && user2Response) {
      console.log("✅ Both users registered successfully!");
      console.log("User 1 socket ID:", socket1.id);
      console.log("User 2 socket ID:", socket2.id);

      if (socket1.id !== socket2.id) {
        console.log("✅ Different socket IDs confirmed!");
      } else {
        console.log("❌ Same socket ID - client issue detected!");
      }
    } else {
      console.log("❌ Registration failed");
    }
  } catch (error) {
    console.error("❌ Test failed:", error);
  } finally {
    // Cleanup
    socket1.disconnect();
    socket2.disconnect();
    process.exit(0);
  }
}

// Run the test
testSocketConnections();
