const io = require("socket.io-client");

const SERVER_URL = "http://localhost:3006";
const ADMIN_JWT =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6ImRjNzcwNDJjLTgwMDUtNGU0Yi05YTQ3LWM3YmFiMzk5ZGM4ZiIsInBob25lIjoiODg4ODg4ODg4OCIsImlhdCI6MTc1MTIyNzUxMywiZXhwIjoxNzgyNzYzNTEzfQ.nK7wseqypO4HgmH6r5FexLlJHppJrGyqa5SmDYGWECQ";
const USER1_JWT =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6ImJmZTUwOTZmLTlhZDItNDBhZS1hZWY0LWQzOGQ5NjI1YjZkMCIsInBob25lIjoiOTg3NjU0MzIxMCIsImlhdCI6MTc1MTIyMjg2OSwiZXhwIjoxNzgyNzU4ODY5fQ.OtdndjtNqjQ4-JfaUd3DzIFOubyGiFGBdqkBzAOkJRY";
const USER2_JWT =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6ImM1NzQ4NmZiLTAwNzItNDQzMi1iNTgyLWNiMmJkZDMzZjdhOCIsInBob25lIjoiOTk5OTk5OTk5OSIsImlhdCI6MTc1MTIyNDE4MywiZXhwIjoxNzgyNzYwMTgzfQ.YourJWTTokenHere";

async function testSocketIDs() {
  console.log("🧪 Testing Socket ID Uniqueness...\n");

  // Create multiple sockets
  const socket1 = io(SERVER_URL);
  const socket2 = io(SERVER_URL);
  const socket3 = io(SERVER_URL);

  try {
    // Wait for connections
    await new Promise((resolve) => setTimeout(resolve, 2000));

    console.log("📡 Socket IDs:");
    console.log("Socket 1 ID:", socket1.id);
    console.log("Socket 2 ID:", socket2.id);
    console.log("Socket 3 ID:", socket3.id);

    // Check if IDs are unique
    const ids = [socket1.id, socket2.id, socket3.id];
    const uniqueIds = [...new Set(ids)];

    if (ids.length === uniqueIds.length) {
      console.log("✅ All socket IDs are unique!");
    } else {
      console.log("❌ Duplicate socket IDs found!");
      console.log("Original IDs:", ids);
      console.log("Unique IDs:", uniqueIds);
    }

    // Test user registration with unique IDs
    console.log("\n📡 Testing User Registration with Unique IDs...");

    // Register users
    socket1.emit("registerUser", {
      action: "registerUser",
      jwt: USER1_JWT,
      auctionId: "test-auction-123",
    });

    socket2.emit("registerUser", {
      action: "registerUser",
      jwt: USER2_JWT,
      auctionId: "test-auction-123",
    });

    let user1Registered = false;
    let user2Registered = false;

    socket1.once("registerUser", (data) => {
      console.log("✅ User 1 registered with socket ID:", socket1.id);
      user1Registered = true;
    });

    socket2.once("registerUser", (data) => {
      console.log("✅ User 2 registered with socket ID:", socket2.id);
      user2Registered = true;
    });

    // Wait for registrations
    await new Promise((resolve) => setTimeout(resolve, 3000));

    if (user1Registered && user2Registered) {
      console.log(
        "✅ Both users registered successfully with different socket IDs!"
      );
    } else {
      console.log("❌ User registration failed");
    }
  } catch (error) {
    console.error("❌ Test failed:", error);
  } finally {
    // Cleanup
    socket1.disconnect();
    socket2.disconnect();
    socket3.disconnect();
    process.exit(0);
  }
}

// Run the test
testSocketIDs();
