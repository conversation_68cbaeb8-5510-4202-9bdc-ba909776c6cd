name: flutter_boilerplate
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+3

environment:
  sdk: '>=3.0.6 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2

  delayed_widget: ^1.1.2

  easy_localization: ^3.0.2
  encrypt: ^5.0.1

  flutter_dotenv: ^5.1.0
  flutter_gen: ^5.3.1

  go_router: ^14.8.1

  provider: ^6.0.5

  shared_preferences: ^2.2.0

  http: ^1.1.0
  network_image_mock: ^2.1.1
  mocktail_image_network: ^1.2.0

  json_annotation: ^4.8.1
  uuid: ^4.5.1

  image_picker: ^1.0.1
  image_picker_for_web: ^3.0.0
  nock: ^1.2.2
  intl_phone_field: ^3.2.0
  webview_flutter: ^4.2.2
  fluttertoast:
    git:
      url: https://github.com/ponnamkarthik/FlutterToast.git
  internet_connection_checker: ^3.0.1
  flutter_expandable_fab: ^2.0.0
  file_picker: ^9.0.2
  path_provider: ^2.1.1
  csv: ^6.0.0
  share_plus: ^10.1.4
  permission_handler: ^11.3.1
  device_info_plus: ^11.2.1
  universal_html: ^2.2.4
  glassmorphism: ^3.0.0
  flutter_animate: ^4.5.2
  socket_io_client: ^3.1.2

dev_dependencies:
  build_runner: ^2.4.6
  flutter_gen_runner:
  flutter_test:
    sdk: flutter
  mockito: ^5.4.2
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  json_serializable: ^6.7.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/png/
    - assets/translations/
    - .env

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Montserrat
      fonts:
        - asset: assets/font/montserrat/Montserrat-Black.ttf
        - asset: assets/font/montserrat/Montserrat-BlackItalic.ttf
        - asset: assets/font/montserrat/Montserrat-Bold.ttf
        - asset: assets/font/montserrat/Montserrat-BoldItalic.ttf
        - asset: assets/font/montserrat/Montserrat-ExtraBold.ttf
        - asset: assets/font/montserrat/Montserrat-ExtraBoldItalic.ttf
        - asset: assets/font/montserrat/Montserrat-ExtraLight.ttf
        - asset: assets/font/montserrat/Montserrat-ExtraLightItalic.ttf
        - asset: assets/font/montserrat/Montserrat-Italic.ttf
        - asset: assets/font/montserrat/Montserrat-Italic-VariableFont_wght.ttf
        - asset: assets/font/montserrat/Montserrat-Light.ttf
        - asset: assets/font/montserrat/Montserrat-LightItalic.ttf
        - asset: assets/font/montserrat/Montserrat-Medium.ttf
        - asset: assets/font/montserrat/Montserrat-MediumItalic.ttf
        - asset: assets/font/montserrat/Montserrat-Regular.ttf
        - asset: assets/font/montserrat/Montserrat-SemiBold.ttf
        - asset: assets/font/montserrat/Montserrat-SemiBoldItalic.ttf
        - asset: assets/font/montserrat/Montserrat-Thin.ttf
        - asset: assets/font/montserrat/Montserrat-ThinItalic.ttf
        - asset: assets/font/montserrat/Montserrat-VariableFont_wght.ttf
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
