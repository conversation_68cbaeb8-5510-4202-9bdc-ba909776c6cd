import 'package:flutter/cupertino.dart';
import 'package:flutter_boilerplate/authentication/user_repository.dart';
import 'package:flutter_boilerplate/view/screens/auction/auction_screen.dart';
import 'package:provider/provider.dart';

import '../app_manager/helper/navigation/navigation_helper.dart';
import '../view/screens/screens.dart';

Future checkLoginStatus(
  BuildContext context,
) async {
  await Future.delayed(const Duration(seconds: 1), () async {
    bool isLoggedIn =
        Provider.of<UserRepository>(context, listen: false).isLoggedIn;
    if (isLoggedIn) {
      int userRole =
          Provider.of<UserRepository>(context, listen: false).currentUser!.role;
      navigate(userRole, context);
    } else {
      NavigationHelper.pushNamed(context, SignInScreen.name);
    }
  });
}

navigate(int userRole, BuildContext context) {
  if (userRole == 1) {
    NavigationHelper.pushNamed(context, AdminDashboard.name);
  } else if (userRole == 2) {
    NavigationHelper.pushNamed(context, AuctionScreen.name);
  }
}
