import 'dart:convert';
import 'dart:io';

import 'package:csv/csv.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';

class CSVConverter {
  static Future<File> myCsv(
      List<String> headerRow, List<List<String>> listOfListOfStrings,
      {bool sharing = false}) async {
    List<List<String>> headerAndDataList = [];
    headerAndDataList.add(headerRow);
    for (var dataRow in listOfListOfStrings) {
      headerAndDataList.add(dataRow);
    }
    String csvData = const ListToCsvConverter().convert(headerAndDataList);
    final bytes = utf8.encode(csvData);
    Uint8List bytes2 = Uint8List.fromList(bytes);
    // MimeType type = MimeType.csv;
    final directory = Platform.isAndroid
        ? await getExternalStorageDirectory() //FOR ANDROID
        : await getApplicationSupportDirectory();
    final filePath = '${directory?.path}/allusers';
    // log("filepath " + filePath);
    final File file = File(filePath);
    await file.writeAsBytes(bytes2);
    return file;
  }
}
