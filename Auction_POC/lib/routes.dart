import 'package:flutter/material.dart';
import 'package:flutter_boilerplate/view/screens/auction/auction_screen.dart';
import 'package:flutter_boilerplate/view/screens/auction/auction_upcoming_player_list.dart';
import 'package:flutter_boilerplate/view/screens/auction/bidders_info_screen.dart';
import 'package:flutter_boilerplate/view/screens/dashboard/all_player_balance.dart';
import 'package:flutter_boilerplate/view/screens/dashboard/user_balance.dart';
import 'package:flutter_boilerplate/view_model/auction_view_model.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'view/screens/screens.dart';
import 'view_model/veiw_model.dart';

// define for transition animation
CustomTransitionPage buildPageWithDefaultTransition<T>({
  required BuildContext context,
  required GoRouterState state,
  required Widget child,
}) {
  return CustomTransitionPage<T>(
    key: state.pageKey,
    child: child,
    transitionsBuilder: (context, animation, secondaryAnimation, child) =>
        FadeTransition(
      opacity: CurveTween(curve: Curves.easeInOutCirc).animate(animation),
      child: child,
    ),
  );
}

List<RouteBase> routes = [
  GoRoute(
    name: SplashScreen.name,
    path: SplashScreen.path,
    pageBuilder: (context, state) => buildPageWithDefaultTransition(
        context: context, state: state, child: const SplashScreen()),
  ),

  GoRoute(
    name: AuctionScreen.name,
    path: AuctionScreen.path,
    pageBuilder: (context, state) => buildPageWithDefaultTransition(
      context: context,
      state: state,
      child: MultiProvider(providers: [
        ChangeNotifierProvider<AuctionViewModel>(
          create: (_) => AuctionViewModel(),
        )
      ], child: AuctionScreen()),
    ),
  ),
  GoRoute(
    name: AdminDashboard.name,
    path: AdminDashboard.path,
    pageBuilder: (context, state) => buildPageWithDefaultTransition(
        context: context, state: state, child:  const AdminDashboard()),
  ),
  GoRoute(
    name: UserBalance.name,
    path: UserBalance.path,
    pageBuilder: (context, state) => buildPageWithDefaultTransition(
        context: context, state: state, child:  const UserBalance()),
  ),
  GoRoute(
    name: SignInScreen.name,
    path: SignInScreen.path,
    pageBuilder: (context, state) => buildPageWithDefaultTransition(
        context: context,
        state: state,
        child: MultiProvider(providers: [
          ChangeNotifierProvider<SignInViewModel>(
            create: (_) => SignInViewModel(),
          ),
        ], child: const SignInScreen())),
  ),
  // GoRoute(
  //   name: NavigationScreen.name,
  //   path: NavigationScreen.path,
  //   pageBuilder: (context, state) => buildPageWithDefaultTransition(
  //       context: context, state: state, child: const NavigationScreen()),
  // ),
  GoRoute(
    name: BottomTabNavigation.name,
    path: BottomTabNavigation.path,
    pageBuilder: (context, state) => buildPageWithDefaultTransition(
        context: context, state: state, child: const BottomTabNavigation()),
  ),
  GoRoute(
    name: UserDetailsScreen.name,
    path: UserDetailsScreen.path,
    pageBuilder: (context, state) => buildPageWithDefaultTransition(
        context: context, state: state, child: UserDetailsScreen()),
  ),
  GoRoute(
    name: AllPlayerBalance.name,
    path: AllPlayerBalance.path,
    pageBuilder: (context, state) => buildPageWithDefaultTransition(
        context: context, state: state, child: AllPlayerBalance()),
  ),
  GoRoute(
    name: BiddersInfo.name,
    path: BiddersInfo.path,
    pageBuilder: (context, state) => buildPageWithDefaultTransition(
        context: context, state: state, child: BiddersInfo()),
  ),
  GoRoute(
    name: UpcomingPlayers.name,
    path: UpcomingPlayers.path,
    pageBuilder: (context, state) => buildPageWithDefaultTransition(
        context: context, state: state, child: UpcomingPlayers()),
  ),
];
