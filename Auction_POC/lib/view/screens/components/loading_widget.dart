import 'package:flutter/material.dart';

loadingWidget() {
  return Center(
    child: <PERSON>umn(
      mainAxisAlignment: MainAxisAlignment.center,
      children: const <Widget>[
        CircularProgressIndicator(), // A loading spinner
        SizedBox(height: 16.0), // Some spacing
        Text(
          'Loading...',
          style: TextStyle(fontSize: 18.0),
        ),
      ],
    ),
  );
}
