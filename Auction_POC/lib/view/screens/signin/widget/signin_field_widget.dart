import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boilerplate/app_manager/enum/button_status.dart';
import 'package:flutter_boilerplate/app_manager/helper/navigation/navigation_helper.dart';
import 'package:flutter_boilerplate/app_manager/helper/show_toast.dart';
import 'package:flutter_boilerplate/app_manager/helper/validation_helper.dart';
import 'package:flutter_boilerplate/app_manager/service/navigation_service.dart';
import 'package:flutter_boilerplate/authentication/user.dart';
import 'package:flutter_boilerplate/authentication/user_repository.dart';
import 'package:flutter_boilerplate/view/screens/auction/auction_screen.dart';
import 'package:flutter_boilerplate/view/screens/components/loader_button.dart';
import 'package:flutter_boilerplate/view/screens/screens.dart';
import 'package:flutter_boilerplate/view_model/signin_view_model.dart';
import 'package:provider/provider.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:flutter_boilerplate/app_manager/component/password_field.dart';

class SignInFieldWidget extends StatefulWidget {
  final SignInViewModel viewModel;
  final UserRepository userRepository;

  const SignInFieldWidget(
      {super.key, required this.viewModel, required this.userRepository});

  @override
  State<SignInFieldWidget> createState() => _SignInFieldWidgetState();
}

class _SignInFieldWidgetState extends State<SignInFieldWidget> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Center(
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 400),
            child: Form(
              child: Builder(builder: (ctx) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      key: const Key("sign_in"),
                      "sign_in",
                      style: theme.textTheme.headlineMedium,
                    ).tr(),
                    const SizedBox(
                      height: 10,
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    IntlPhoneField(
                      key: const Key("tf_mobile_number"),
                      controller: widget.viewModel.phoneC,
                      decoration: InputDecoration(
                        errorStyle: const TextStyle(color: Colors.red),
                        hintText: 'mobile_number'.tr(),
                        hintStyle:
                            TextStyle(color: Colors.grey.withOpacity(0.7)),
                        floatingLabelStyle:
                            TextStyle(color: theme.primaryColor),
                        border: const OutlineInputBorder(
                          borderSide: BorderSide(),
                        ),
                      ),
                      initialCountryCode: 'IN',
                      onChanged: (phone) {
                        try {
                          if (phone.isValidNumber()) {
                            widget.viewModel.phoneC.text =
                                phone.number.toString();
                            widget.viewModel.setIsValidNumber = true;
                          } else {
                            widget.viewModel.setIsValidNumber = false;
                          }
                        } catch (e) {
                          if (kDebugMode) {
                            print(e);
                          }
                        }
                      },
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    PasswordField(
                      key: const Key("tf_password"),
                      controller: widget.viewModel.passwordC,
                      hintText: "enter_password".tr(),
                      validator: ValidationHelper.passwordValidationSignIn,
                      onFieldSubmitted: (val) {
                        onPressSignIn(ctx);
                      },
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Selector<SignInViewModel, ButtonStatus>(
                        shouldRebuild: (prev, nex) => true,
                        selector: (buildContext, vm) => vm.loginStatus,
                        builder: (context, ButtonStatus status, child) {
                          return LoaderButton(
                            label: "sign_in".tr(),
                            loading: status == ButtonStatus.hit,
                            key: const Key("tb_sign_in"),
                            onPressed: () {
                              onPressSignIn(ctx);
                            },
                          );
                        }),
                    const SizedBox(
                      height: 20,
                    ),
                  ],
                );
              }),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> onPressSignIn(BuildContext ctx) async {
    if (Form.of(ctx).validate() && widget.viewModel.isValidNumber) {
      User? user = await widget.viewModel.signIn(
          phone: widget.viewModel.phoneC.text,
          password: widget.viewModel.passwordC.text);
      if (user != null) {
        await widget.userRepository.updateUserData(user);
        if (user.role == 1) {
          NavigationHelper.pushNamed(
              NavigationService.context!, AdminDashboard.name);
        } else if (user.role == 2) {
          NavigationHelper.pushNamed(
              NavigationService.context!, AuctionScreen.name);
        }
      }
      // });
    } else {
      showToast("validation".tr(gender: "fill_required_fields".tr()));
    }
  }

  void storeAndNavigate(User user) {
    widget.userRepository.updateUserData(user).then((value) {
      NavigationHelper.pushNamed(context, NavigationScreen.name);
    });
  }
}
