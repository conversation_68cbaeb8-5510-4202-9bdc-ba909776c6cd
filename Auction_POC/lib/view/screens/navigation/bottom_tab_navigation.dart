import 'package:flutter/material.dart';
import 'package:flutter_boilerplate/view/screens/auction/auction_screen.dart';
import 'package:flutter_boilerplate/view/screens/dashboard/admin_dashboard.dart';

class BottomTabNavigation extends StatefulWidget {
  const BottomTabNavigation({super.key});
  static const String name = "bottom_tab_navigation";
  static const String path = "/$name";

  @override
  State<BottomTabNavigation> createState() => _BottomTabNavigationState();
}

class _BottomTabNavigationState extends State<BottomTabNavigation> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const AuctionScreen(),
    const AdminDashboard(),
    // UserDetailsScreen(
    //   showButton: false,
    // )
  ];

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2, // Number of tabs
      child: Scaffold(
          // appBar: AppBar(
          //   title: Text(
          //     "flutter_boilerplate",
          //     style: theme.textTheme.headlineSmall,
          //   ).tr(),
          // ),
          body: _screens[_currentIndex],
          bottomNavigationBar: BottomNavigationBar(
            backgroundColor: Colors.blue,
            selectedItemColor: Colors.red,
            unselectedItemColor: Colors.black,
            currentIndex: _currentIndex,
            onTap: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            items: const [
              BottomNavigationBarItem(
                icon: Icon(Icons.dashboard),
                label: 'User',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.person),
                label: 'Admin',
              ),
            ],
          )),
    );
  }
}
