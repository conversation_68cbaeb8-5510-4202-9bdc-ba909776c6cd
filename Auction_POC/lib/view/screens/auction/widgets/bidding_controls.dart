import 'package:flutter_boilerplate/util/logger.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boilerplate/app_manager/helper/show_toast.dart';
import 'package:flutter_boilerplate/authentication/user_repository.dart';
import 'package:flutter_boilerplate/view_model/web_socket_view_model.dart';
import 'package:provider/provider.dart';

class BiddingControls extends StatefulWidget {
  const BiddingControls({super.key});

  @override
  State<BiddingControls> createState() => _BiddingControlsState();
}

class _BiddingControlsState extends State<BiddingControls> {
  final TextEditingController biddingController = TextEditingController();
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<WebSocketViewModel, UserRepository>(
      builder: (context, webSocketViewModel, userRepository, child) {
        final currentBid = webSocketViewModel.auction.leadingBid == 0
            ? webSocketViewModel.auction.basePrice.toDouble()
            : webSocketViewModel.auction.leadingBid.toDouble();

        bool isButtonEnabled = webSocketViewModel.auction.leadingBidderId !=
                userRepository.currentUser?.id &&
            (userRepository.currentUser?.remainingBalance ?? 0) > currentBid &&
            !webSocketViewModel.pauseActivated &&
            webSocketViewModel.buttonEnable;

        return Column(
          children: [
            if (currentBid <=
                (userRepository.currentUser?.remainingBalance ?? -1))
              SizedBox(height: 12),
            ElevatedButton(
              onPressed: isButtonEnabled
                  ? () {
                      int bidIncrement = currentBid < 200000 ? 5000 : 10000;
                      final bid = (currentBid + bidIncrement).toInt();

                      if (bid <= currentBid) {
                        showToast("Bid should be greater than current bid");
                      } else if (userRepository.currentUser!.remainingBalance <
                          bid) {
                        showToast("You don't have enough balance");
                      } else {
                        FocusManager.instance.primaryFocus?.unfocus();
                        log("Placing a bid of: $bid");
                        webSocketViewModel.addBid(
                            userRepository.currentUser?.token ?? '',
                            webSocketViewModel.auction.playerId,
                            bid);
                      }
                    }
                  : null,
              child: Text("Bid", style: TextStyle(fontSize: 20)),
            ),
            SizedBox(height: 12),
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 20.0, vertical: 10),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      enabled: isButtonEnabled,
                      keyboardType: TextInputType.number,
                      textAlign: TextAlign.center,
                      controller: biddingController,
                      decoration: InputDecoration(
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.blue.shade400),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.blue.shade100),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 12),
                  ElevatedButton(
                    onPressed: isButtonEnabled
                        ? () {
                            final bid = currentBid.toInt() +
                                (int.tryParse(biddingController.text.trim()) ??
                                    0);
                            if (bid <= currentBid) {
                              showToast(
                                  "Bid should be greater than current bid");
                            } else if (userRepository
                                    .currentUser!.remainingBalance <
                                bid) {
                              showToast("You don't have enough balance");
                            } else {
                              FocusManager.instance.primaryFocus?.unfocus();
                              log("Placing a bid of: $bid");
                              webSocketViewModel.addBid(
                                  userRepository.currentUser?.token ?? '',
                                  webSocketViewModel.auction.playerId,
                                  bid);
                            }
                          }
                        : null,
                    child: Text("Submit"),
                  ),
                ],
              ),
            ),
            SizedBox(height: 12),
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    biddingController.dispose();
    super.dispose();
  }
}
