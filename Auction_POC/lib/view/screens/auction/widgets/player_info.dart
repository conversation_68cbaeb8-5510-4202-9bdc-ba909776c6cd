import 'package:flutter/material.dart';
import 'package:flutter_boilerplate/view_model/web_socket_view_model.dart';
import 'package:provider/provider.dart';

class PlayerInfo extends StatelessWidget {
  const PlayerInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<WebSocketViewModel>(
      builder: (context, viewModel, child) {
        return Column(
          children: [
            CircleAvatar(
              backgroundImage: AssetImage('assets/png/photo.jpg'),
              radius: 60,
              backgroundColor: Colors.grey.shade200,
            ),
            SizedBox(height: 16),
            Text(
              viewModel.auction.playerName,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue.shade900,
              ),
            ),
            Text(
              '(${viewModel.auction.playerType})',
              style: TextStyle(
                fontSize: 14,
                color: Colors.blue.shade600,
              ),
            ),
            SizedBox(height: 16),
            if (viewModel.auction.leadingBidder.isNotEmpty)
              Column(
                children: [
                  Text(
                    'Leading Bidder',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade900,
                    ),
                  ),
                  Text(
                    viewModel.auction.leadingBidder,
                    style: TextStyle(
                      color: Colors.blueGrey.shade700,
                    ),
                  ),
                  SizedBox(height: 30),
                ],
              ),
          ],
        );
      },
    );
  }
} 