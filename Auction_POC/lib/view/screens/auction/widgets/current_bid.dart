import 'package:flutter/material.dart';
import 'package:flutter_boilerplate/view_model/web_socket_view_model.dart';
import 'package:provider/provider.dart';

class CurrentBid extends StatelessWidget {
  const CurrentBid({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<WebSocketViewModel>(
      builder: (context, viewModel, child) {
        final currentBid = viewModel.auction.leadingBid == 0
            ? viewModel.auction.basePrice.toDouble()
            : viewModel.auction.leadingBid.toDouble();

        return Container(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Current Bid: ',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade800,
                ),
              ),
              Text(
                '\$${currentBid.toInt()}',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade900,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
} 