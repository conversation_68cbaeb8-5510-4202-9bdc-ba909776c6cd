import 'package:flutter/material.dart';
import 'package:flutter_boilerplate/view_model/web_socket_view_model.dart';
import 'package:provider/provider.dart';

class AuctionTimer extends StatelessWidget {
  const AuctionTimer({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<WebSocketViewModel>(
      builder: (context, viewModel, child) {
        final remainingTime =  viewModel.timerDuration - viewModel.elapsedSeconds;
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.green.shade50,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Text(
            'Hurry! Last Bid call Only $remainingTime seconds remaining!',
            style: TextStyle(
              fontSize: 16.0,
              fontWeight: FontWeight.bold,
              color: Colors.green.shade700,
            ),
          ),
        );
      },
    );
  }
} 