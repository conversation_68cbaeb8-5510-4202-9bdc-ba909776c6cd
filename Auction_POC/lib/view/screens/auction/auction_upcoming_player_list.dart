import 'package:flutter/material.dart';
import 'package:flutter_boilerplate/view_model/web_socket_view_model.dart';
import 'package:provider/provider.dart';

class UpcomingPlayers extends StatelessWidget {
  const UpcomingPlayers({super.key});
  static const String name = "UpcomingPlayers";
  static const String path = "/$name";

  @override
  Widget build(BuildContext context) {
    return Consumer<WebSocketViewModel>(
      builder: (context, webSocketViewModel, child) {
        final upcomingPlayers = webSocketViewModel.upcomingPlayers;
        final playersList = upcomingPlayers.players;

        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.5),
                spreadRadius: 2,
                blurRadius: 7,
                offset: Offset(0, -3),
              ),
            ],
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Upcoming Auction Players',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade900,
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      Icons.close,
                      color: Colors.grey.shade900,
                    ),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Flexible(
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.vertical,
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: DataTable(
                        headingRowColor: WidgetStateColor.resolveWith(
                            (states) => Colors.deepPurple.shade50),
                        dataRowMaxHeight: 60,
                        columnSpacing: 20,
                        columns: const <DataColumn>[
                          DataColumn(
                            label: Text(
                              'Player Name',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.deepPurple,
                              ),
                            ),
                          ),
                          DataColumn(
                            label: Text(
                              'Type',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.deepPurple,
                              ),
                            ),
                          ),
                          DataColumn(
                            label: Text(
                              'Base Price',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.deepPurple,
                              ),
                            ),
                          ),
                        ],
                        rows: playersList != null
                            ? playersList.map((player) {
                                return DataRow(
                                  cells: <DataCell>[
                                    DataCell(Text(
                                      player.name,
                                      style: TextStyle(
                                        color: Colors.grey.shade800,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    )),
                                    DataCell(Text(
                                      player.playerType?.type ?? 'N/A',
                                      style: TextStyle(
                                        color: Colors.purple.shade700,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    )),
                                    DataCell(Text(
                                      '\$${player.basePrice}',
                                      style: TextStyle(
                                        color: Colors.green.shade700,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    )),
                                  ],
                                );
                              }).toList()
                            : [],
                      ),
                    ),
                  ),
                ),
              ),
              if (playersList == null || playersList.isEmpty)
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Center(
                    child: Text(
                      'No upcoming players available',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}
