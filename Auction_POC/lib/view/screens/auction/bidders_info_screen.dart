import 'package:flutter_boilerplate/util/logger.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boilerplate/app_manager/api/project_response.dart';
import 'package:flutter_boilerplate/view_model/web_socket_view_model.dart';
import 'package:provider/provider.dart';

class BiddersInfo extends StatelessWidget {
  const BiddersInfo({super.key});
  static const String name = "BiddersInfo";
  static const String path = "/$name";

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Consumer<WebSocketViewModel>(
      builder: (context, viewModel, child) {
        final users = viewModel.liveBidders.data?.users ?? [];
        final playerTypeCount =
            viewModel.liveBidders.data?.playerTypeCounts ?? PlayerTypeCounts();
        final auctionData =
            viewModel.liveBidders.data?.auctionData ?? AuctionData();

        log("Live Auction users from WebSocket: ${users.length}");

        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.5),
                spreadRadius: 2,
                blurRadius: 7,
                offset: const Offset(0, -3),
              ),
            ],
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      "Participating Bidders' Balances",
                      style: theme.textTheme.headlineSmall,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      Icons.close,
                      color: theme.iconTheme.color,
                    ),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              users.isEmpty
                  ? const Padding(
                      padding: EdgeInsets.all(20.0),
                      child: Center(child: Text('No user data available.')),
                    )
                  : Flexible(
                      child: ConstrainedBox(
                        constraints: BoxConstraints(
                          maxHeight: MediaQuery.of(context).size.height * 0.6,
                        ),
                        child: Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: SingleChildScrollView(
                            scrollDirection: Axis.vertical,
                            child: SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: DataTable(
                                headingRowColor: WidgetStateColor.resolveWith(
                                    (states) => Colors.deepPurple.shade50),
                                columnSpacing: 20,
                                columns: const [
                                  DataColumn(
                                    label: Text(
                                      'User Name',
                                      style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: Colors.deepPurple),
                                    ),
                                  ),
                                  DataColumn(
                                    label: Text(
                                      'BAT',
                                      style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: Colors.deepPurple),
                                    ),
                                  ),
                                  DataColumn(
                                    label: Text(
                                      'BWL',
                                      style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: Colors.deepPurple),
                                    ),
                                  ),
                                  DataColumn(
                                    label: Text(
                                      'WK',
                                      style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: Colors.deepPurple),
                                    ),
                                  ),
                                  DataColumn(
                                    label: Text(
                                      'Bonus Balance',
                                      style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: Colors.deepPurple),
                                    ),
                                  ),
                                  DataColumn(
                                    label: Text(
                                      'Remaining Balance',
                                      style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: Colors.deepPurple),
                                    ),
                                  ),
                                ],
                                rows: users.map((user) {
                                  return DataRow(
                                    cells: [
                                      DataCell(Text(user.name,
                                          style: TextStyle(
                                              color: Colors.grey.shade800))),
                                      DataCell(Text(
                                        (user.purchaseSummary?.batsmen ?? 0)
                                            .toString(),
                                        style: TextStyle(
                                            color: Colors.green.shade700,
                                            fontWeight: FontWeight.bold),
                                      )),
                                      DataCell(Text(
                                        (user.purchaseSummary?.bowlers ?? 0)
                                            .toString(),
                                        style: TextStyle(
                                            color: Colors.green.shade700,
                                            fontWeight: FontWeight.bold),
                                      )),
                                      DataCell(Text(
                                        (user.purchaseSummary?.wicketKeepers ??
                                                0)
                                            .toString(),
                                        style: TextStyle(
                                            color: Colors.green.shade700,
                                            fontWeight: FontWeight.bold),
                                      )),
                                      DataCell(Text(
                                        '₹${user.userBonusBalance}',
                                        style: TextStyle(
                                            color: Colors.blue.shade700,
                                            fontWeight: FontWeight.bold),
                                      )),
                                      DataCell(Text(
                                        '₹${user.remainingBalance}',
                                        style: TextStyle(
                                            color: Colors.blue.shade700,
                                            fontWeight: FontWeight.bold),
                                      )),
                                    ],
                                  );
                                }).toList(),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
            ],
          ),
        );
      },
    );
  }
}
