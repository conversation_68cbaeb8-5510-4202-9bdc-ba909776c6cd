import 'package:flutter/material.dart';
import 'package:flutter_boilerplate/view_model/web_socket_view_model.dart';
import 'package:flutter_boilerplate/authentication/user_repository.dart';
import 'package:provider/provider.dart';

class StatusText extends StatelessWidget {
  const StatusText({super.key});

  @override
  Widget build(BuildContext context) {
    final webSocketViewModel =
        Provider.of<WebSocketViewModel>(context, listen: true);
    final userRepository = Provider.of<UserRepository>(context, listen: true);

    final text = webSocketViewModel.transaction.text?.text;
    final currentBid = webSocketViewModel.auction.leadingBid == 0
        ? webSocketViewModel.auction.basePrice.toDouble()
        : webSocketViewModel.auction.leadingBid.toDouble();

    final hasSufficientBalance =
        userRepository.currentUser!.remainingBalance >= currentBid;

    String displayText = '';
    if (!hasSufficientBalance) {
      displayText = "You don't have sufficient balance.";
    } else if (text != null && text.trim().isNotEmpty) {
      displayText = text;
    }

    return displayText.isEmpty
        ? Container()
        : Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Text(
              displayText,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey),
            ),
          );
  }
}
