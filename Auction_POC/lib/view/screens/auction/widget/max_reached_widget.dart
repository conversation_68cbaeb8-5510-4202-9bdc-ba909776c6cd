import 'package:flutter/material.dart';
import 'package:flutter_boilerplate/models/auction.dart';
import 'package:flutter_boilerplate/models/bid_transaction.dart';
import 'package:flutter_boilerplate/view_model/web_socket_view_model.dart';
import 'package:flutter_boilerplate/authentication/user_repository.dart';
import 'package:provider/provider.dart';

class MaxReachedWidget extends StatelessWidget {
  const MaxReachedWidget({super.key});

  int maxReached(BidTransaction transaction, String playerType) {
    switch (playerType) {
      case "WK":
        return transaction.text?.wicketKeeperMax ?? 0;
      case "BAT":
        return transaction.text?.batterMax ?? 0;
      case "BALL":
        return transaction.text?.bowlerMax ?? 0;
      default:
        return 0;
    }
  }

  String playerTypeName(String playerType) {
    switch (playerType) {
      case "WK":
        return "Wicket Keeper";
      case "BAT":
        return "Batter";
      case "BALL":
        return "Bowler";
      default:
        return "";
    }
  }

  @override
  Widget build(BuildContext context) {
    final WebSocketViewModel webSocketViewModel =
        Provider.of<WebSocketViewModel>(context, listen: true);
    final UserRepository userRepository =
        Provider.of<UserRepository>(context, listen: true);

    Auction auction = webSocketViewModel.auction;
    BidTransaction transaction = webSocketViewModel.transaction;

    final currentBid = auction.leadingBid == 0
        ? auction.basePrice.toDouble()
        : auction.leadingBid.toDouble();

    final hasSufficientBalance =
        userRepository.currentUser!.remainingBalance >= currentBid;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 10),
      child: Selector<WebSocketViewModel, bool>(
        selector: (cx, vm) => vm.transaction.text?.biddable ?? true,
        builder: (context, status, child) {
          if (!status) {
            return Text(
              "You have already purchased ${maxReached(transaction, auction.playerType)} - ${playerTypeName(auction.playerType)}",
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey),
            );
          } else if (!hasSufficientBalance) {
            return Text(
              "You don't have sufficient balance.",
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey),
            );
          } else {
            final text = webSocketViewModel.transaction.text?.text;
            if (text != null && text.trim().isNotEmpty) {
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Text(
                  text,
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.grey),
                ),
              );
            } else {
              return Container();
            }
          }
        },
      ),
    );
  }
}
