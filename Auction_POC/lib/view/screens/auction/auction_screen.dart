import 'package:flutter/material.dart';
import 'package:flutter_boilerplate/authentication/user_repository.dart';
import 'package:flutter_boilerplate/view/screens/auction/auction_upcoming_player_list.dart';
import 'package:flutter_boilerplate/view/screens/auction/widget/max_reached_widget.dart';
import 'package:flutter_boilerplate/view/screens/auction/widgets/bidding_controls.dart';
import 'package:flutter_boilerplate/view/screens/auction/widgets/current_bid.dart';
import 'package:flutter_boilerplate/view/screens/auction/widgets/no_auction_widget.dart';
import 'package:flutter_boilerplate/view/screens/auction/widgets/player_info.dart';
import 'package:flutter_boilerplate/view/screens/auction/widgets/user_wallet_details.dart';
import 'package:flutter_boilerplate/view/screens/auction/bidders_info_screen.dart';
import 'package:flutter_boilerplate/view/screens/auction/purchased_players_history.dart';
import 'package:flutter_boilerplate/view/screens/auction/widgets/waiting_widget.dart';
import 'package:flutter_boilerplate/view/widgets/network_aware_widget.dart';
import 'package:flutter_boilerplate/view_model/web_socket_view_model.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:provider/provider.dart';
import 'dart:async';

class AuctionScreen extends StatefulWidget {
  const AuctionScreen({super.key});

  static const String name = "auction-screen";
  static const String path = "/$name";

  @override
  State<AuctionScreen> createState() => _AuctionScreenState();
}

class _AuctionScreenState extends State<AuctionScreen>
    with WidgetsBindingObserver {
  final Color primaryBlue = Colors.blue.shade400;
  final Color secondaryBlue = Colors.blue.shade100;
  final Color accentColor = Colors.amber.shade700;
  late WebSocketViewModel webSocketViewModel;
  late UserRepository userRepository;
  StreamSubscription? _internetListener;
  Timer? _timer;
  bool _isWaitingForNextBid = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    userRepository = Provider.of<UserRepository>(context, listen: false);
    webSocketViewModel =
        Provider.of<WebSocketViewModel>(context, listen: false);

    WebSocketViewModel.state.addListener(_handleStateChange);
    _setupInternetListener();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      webSocketViewModel.onAuctionPage = true;
      _initializeConnection();
    });
  }

  void _setupInternetListener() {
    final connectionChecker = InternetConnectionChecker.instance;
    _internetListener = connectionChecker.onStatusChange.listen((status) {
      if (status == InternetConnectionStatus.connected) {
        _initializeConnection();
      } else {
        webSocketViewModel.disconnectClient();
      }
    });
  }

  void _initializeConnection() {
    if (webSocketViewModel.socket == null) {
      webSocketViewModel.wsConnection();
      _registerUser();
    }
  }

  void _registerUser() {
    final token = userRepository.currentUser?.token ?? 'DefaultToken';
    webSocketViewModel.registerUserOnSocket(token);
  }

  void _startTimer() {
    _timer?.cancel();
    webSocketViewModel.elapsedSeconds = 0;

    if (webSocketViewModel.timerDuration > 0) {
      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (webSocketViewModel.pauseActivated) return;

        if (webSocketViewModel.resetLastCallTimer) {
          _resetTimer();
        } else if (webSocketViewModel.elapsedSeconds <
            webSocketViewModel.timerDuration) {
          setState(() {
            webSocketViewModel.elapsedSeconds++;
          });
        } else {
          timer.cancel();
        }
      });
    }
  }

  void _resetTimer() {
    _timer?.cancel();
    setState(() {
      webSocketViewModel.resetLastCallTimer = false;
      webSocketViewModel.elapsedSeconds = 0;
    });
  }

  Future<void> _handleStateChange() async {
    // await userRepository.fetchUser();
    final state = WebSocketViewModel.state.value;

    setState(() {
      _isWaitingForNextBid = state == AuctionState.playerUnsold ||
          state == AuctionState.soldPlayer ||
          state == AuctionState.success;

      if (state == AuctionState.lastCall) {
        webSocketViewModel.isLastCallTimerDisplay = true;
        _startTimer();
      } else {
        _resetTimer();
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    _internetListener?.cancel();
    WebSocketViewModel.state.removeListener(_handleStateChange);
    Future.microtask(() {
      if (mounted) return;
      webSocketViewModel.onAuctionPage = false;
    });
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<WebSocketViewModel>(
      builder: (context, viewModel, child) {
        return PopScope(
          onPopInvoked: (didPop) {
            // viewModel.getAuctionStatus(userRepository.currentUser?.token ?? '');
          },
          child: Scaffold(
            backgroundColor: Colors.grey.shade100,
            appBar: AppBar(
              title: const Text('Auction Screen',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
              automaticallyImplyLeading: false,
              actions: [
                IconButton(
                  icon: const Icon(Icons.logout),
                  onPressed: () {
                    _timer?.cancel();
                    userRepository.signOutUser(context);
                  },
                ),
              ],
            ),
            body: NetworkAwareWidget(
              onReconnect: _initializeConnection,
              offlineChild: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: const [
                    Icon(Icons.wifi_off, size: 80, color: Colors.grey),
                    SizedBox(height: 16),
                    Text(
                      "No network connectivity",
                      style: TextStyle(
                        fontSize: 20.0,
                        color: Colors.black87,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              onlineChild: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16.0, vertical: 12.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          UserWalletBalance(),
                          const SizedBox(height: 16),
                          if (viewModel.isLastCallTimerDisplay)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 8),
                              decoration: BoxDecoration(
                                color: Colors.green.shade50,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Text(
                                'Hurry! Last Bid call please add your bid quickly',
                                style: TextStyle(
                                  fontSize: 16.0,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.green.shade700,
                                ),
                              ),
                            ),
                          const SizedBox(height: 16),
                          viewModel.auction.playerId.isEmpty
                              ? const NoAuctionWidget()
                              : _isWaitingForNextBid
                                  ? const WaitingForNextBidWidget()
                                  : const PlayerInfo(),
                          const SizedBox(height: 20),
                          if (viewModel.auction.playerId.isNotEmpty &&
                              !_isWaitingForNextBid) ...[
                            Column(
                              children: [
                                CurrentBid(),
                                const SizedBox(height: 16),
                                BiddingControls(),
                                const SizedBox(height: 16),
                                const MaxReachedWidget()
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                  _buildBottomNavigation(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildNavButton(
            icon: Icons.person,
            label: 'My Players',
            onPressed: () => _showBottomSheet(const PurchasedPlayersSheet()),
            // onPressed: () {
            //   final token = userRepository.currentUser?.token ?? '';
            //   webSocketViewModel.getUserDetails(token);
            //   _showBottomSheet(const PurchasedPlayersSheet());
            // },
          ),
          _buildNavButton(
            icon: Icons.leaderboard,
            label: 'Stats',
            onPressed: () {
              final token = userRepository.currentUser?.token ?? '';
              webSocketViewModel.getLiveBidders(token);
              _showBottomSheet(const BiddersInfo());
            },
          ),
          _buildNavButton(
            icon: Icons.format_list_numbered,
            label: 'Lineup',
            onPressed: () {
              final token = userRepository.currentUser?.token ?? '';
              webSocketViewModel.getUpcomingPlayers(token);
              _showBottomSheet(const UpcomingPlayers());
            },
          ),
        ],
      ),
    );
  }

  void _showBottomSheet(Widget child) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (_, controller) => child,
      ),
    );
  }

  Widget _buildNavButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(icon, color: primaryBlue, size: 28),
                const SizedBox(height: 4),
                Text(
                  label,
                  style: TextStyle(
                    color: primaryBlue,
                    fontWeight: FontWeight.w500,
                    fontSize: 13,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
