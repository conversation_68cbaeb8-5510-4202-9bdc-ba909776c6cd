import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boilerplate/app_manager/component/alert_dialog/alert_dialog_view.dart';
import 'package:flutter_boilerplate/app_manager/component/bottom_sheet/custom_bottom_sheet.dart';
import 'package:flutter_boilerplate/app_manager/component/bottom_sheet/functional_sheet.dart';
import 'package:flutter_boilerplate/authentication/user_repository.dart';
import 'package:flutter_boilerplate/view/screens/dashboard/all_player_balance.dart';
import 'package:flutter_boilerplate/view/screens/dashboard/user_balance.dart';
import 'package:flutter_boilerplate/view/screens/dashboard/widget/players_table.dart';
import 'package:flutter_boilerplate/view/screens/dashboard/widget/status_log.dart';
import 'package:flutter_boilerplate/view/widgets/network_aware_widget.dart';
import 'package:flutter_boilerplate/view_model/admin_dashboard_repository.dart';
import 'package:flutter_boilerplate/view_model/admin_dashboard_view_model.dart';
import 'package:flutter_boilerplate/widget/sync_icon_nutton.dart';
import 'package:flutter_expandable_fab/flutter_expandable_fab.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

class AdminDashboard extends StatefulWidget {
  const AdminDashboard({super.key});

  static const String name = "adminDashboard";
  static const String path = "/$name";

  @override
  State<AdminDashboard> createState() => _AdminDashboardState();
}

class _AdminDashboardState extends State<AdminDashboard> {
  bool isWalletAmountValid = false;
  @override
  void initState() {
    super.initState();
    final viewModel =
        Provider.of<AdminDashboardViewModel>(context, listen: false);
    viewModel.userRepository =
        Provider.of<UserRepository>(context, listen: false);
    viewModel.initSocketConnection();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final viewModel = Provider.of<AdminDashboardViewModel>(context);
    final adminDashboardAPI = Provider.of<AdminDashboardAPI>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Admin Dashboard',
          style: theme.textTheme.headlineSmall,
        ),
        automaticallyImplyLeading: false,
        actions: [
          Selector<AdminDashboardViewModel, (bool, bool)>(
              shouldRebuild: (prev, nex) => true,
              selector: (buildContext, vm) => (vm.hasEventsToSync, vm.syncing),
              builder: (context, data, child) {
                bool hasEventsToSync = data.$1;
                bool syncing = data.$2;
                return hasEventsToSync
                    ? SyncIconButton(
                        isSyncing: syncing,
                        onPressed: () {
                          viewModel.syncAllEvents(context);
                        },
                      )
                    : Container();
              }),
          Container(
            margin: EdgeInsets.only(right: 16),
            child: IconButton(
              icon: Icon(Icons.logout, color: theme.iconTheme.color),
              onPressed: () {
                viewModel.userRepository?.signOutUser(context);
              },
            ),
          )
        ],
      ),
      body: SingleChildScrollView(
        child: NetworkAwareWidget(
          onReconnect: () {
            if (mounted) {
              viewModel.initSocketConnection(reconnect: true);
            }
          },
          offlineChild: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.signal_wifi_off, size: 100, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  "No network connectivity",
                  style: TextStyle(
                      fontSize: 18,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500),
                ),
              ],
            ),
          ),
          onlineChild: SingleChildScrollView(
            child: Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  _buildInputCard(context, viewModel),
                  SizedBox(height: 20),
                  _buildAuctionButton(viewModel),
                  SizedBox(height: 20),
                  viewModel.isLastCallEnabledForPlayer
                      ? _buildStatusAndTimerCard(viewModel)
                      : SizedBox(),
                  SizedBox(height: 20),
                  getAuctionButton(
                      context: context,
                      enabled: true,
                      text: "Participating Bidders' Balances",
                      onPressed: () {
                        final token =
                            viewModel.userRepository?.currentUser?.token ?? '';
                        viewModel.getAllUserDetails(token);
                        showModalBottomSheet(
                          context: context,
                          isScrollControlled: true,
                          backgroundColor: Colors.transparent,
                          builder: (context) => DraggableScrollableSheet(
                            initialChildSize: 0.7,
                            minChildSize: 0.5,
                            maxChildSize: 1,
                            builder: (_, controller) => AllPlayerBalance(),
                          ),
                        );
                      },
                      visibility: true),
                  SizedBox(height: 12),
                  getAuctionButton(
                      context: context,
                      enabled: true,
                      text: "DB Clean",
                      onPressed: () async {
                        DBCleanMessagePopup();
                      },
                      visibility: true),
                  SizedBox(height: 20),
                  Card(
                    color: isDarkMode ? Colors.grey[850] : Colors.white,
                    elevation: 10,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: SizedBox(
                        height: 500,
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            if (constraints.maxWidth > 600) {
                              return Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: PlayersTable(
                                      dataStream: viewModel.dataStream,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: UserBalance(),
                                  ),
                                ],
                              );
                            } else {
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  PlayersTable(
                                    dataStream: viewModel.dataStream,
                                  ),
                                ],
                              );
                            }
                          },
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      floatingActionButtonLocation: ExpandableFab.location,
      floatingActionButton: ExpandableFab(
        openButtonBuilder: RotateFloatingActionButtonBuilder(
          child: const Icon(Icons.account_box),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          fabSize: ExpandableFabSize.regular,
          shape: const CircleBorder(),
        ),
        closeButtonBuilder: DefaultFloatingActionButtonBuilder(
          child: const Icon(Icons.close),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          fabSize: ExpandableFabSize.small,
          shape: const CircleBorder(),
        ),
        childrenOffset: Offset(3, 3),
        children: [
          FloatingActionButton.small(
            backgroundColor: Colors.blue[300],
            heroTag: null,
            foregroundColor: Colors.white,
            child: const Icon(Icons.upload),
            onPressed: () async {
              await adminDashboardAPI.pickFile();
            },
          ),
          FloatingActionButton.small(
            heroTag: null,
            backgroundColor: Colors.blue[300],
            foregroundColor: Colors.white,
            child: const Icon(Icons.download),
            onPressed: () async {
              await adminDashboardAPI.downloadFile();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInputCard(
      BuildContext context, AdminDashboardViewModel viewModel) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    return Card(
      color: isDarkMode ? Colors.grey[850] : Colors.white,
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            TextField(
              controller: viewModel.walletAmountController,
              style: TextStyle(
                color: isDarkMode ? Colors.grey.shade400 : Colors.black,
              ),
              onChanged: (value) {
                final walletAmount = int.tryParse(value);
                isWalletAmountValid = walletAmount != null && walletAmount > 0;
              },
              enabled: !viewModel.disableAllFields,
              keyboardType: TextInputType.number,
              decoration: inputDecoration(context, 'User Wallet Amount'),
            ),
            const SizedBox(height: 20.0),
            TextField(
              onChanged: (value) {
                viewModel.timerDuration = int.tryParse(value) ?? 0;
                viewModel.originalTimerDuration = int.tryParse(value) ?? 0;
              },
              style: TextStyle(
                color: isDarkMode ? Colors.grey.shade400 : Colors.black,
              ),
              controller: viewModel.timerController,
              keyboardType: TextInputType.number,
              enabled: !viewModel.disableAllFields,
              decoration: inputDecoration(context, 'Enter Timer Duration'),
            ),
            const SizedBox(height: 20.0),
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: TextField(
                    style: TextStyle(
                      color: isDarkMode ? Colors.grey.shade400 : Colors.black,
                    ),
                    controller: viewModel.batterC,
                    keyboardType: TextInputType.number,
                    enabled: !viewModel.disableAllFields,
                    decoration: inputDecoration(context, 'Enter Batsman Count'),
                  ),
                ),
                SizedBox(width: 10),
                Expanded(
                  child: TextField(
                    style: TextStyle(
                      color: isDarkMode ? Colors.grey.shade400 : Colors.black,
                    ),
                    controller: viewModel.batterMaxC,
                    keyboardType: TextInputType.number,
                    enabled: !viewModel.disableAllFields,
                    decoration: inputDecoration(context, 'Max'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20.0),
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: TextField(
                    style: TextStyle(
                      color: isDarkMode ? Colors.grey.shade400 : Colors.black,
                    ),
                    controller: viewModel.bowlerC,
                    keyboardType: TextInputType.number,
                    enabled: !viewModel.disableAllFields,
                    decoration: inputDecoration(context, 'Enter Bowler Count'),
                  ),
                ),
                SizedBox(
                  width: 10,
                ),
                Expanded(
                  child: TextField(
                    style: TextStyle(
                      color: isDarkMode ? Colors.grey.shade400 : Colors.black,
                    ),
                    controller: viewModel.bowlerMaxC,
                    keyboardType: TextInputType.number,
                    enabled: !viewModel.disableAllFields,
                    decoration: inputDecoration(context, 'Max'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20.0),
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: TextField(
                    style: TextStyle(
                      color: isDarkMode ? Colors.grey.shade400 : Colors.black,
                    ),
                    controller: viewModel.wicketKeeperC,
                    keyboardType: TextInputType.number,
                    enabled: !viewModel.disableAllFields,
                    decoration:
                        inputDecoration(context, 'Enter Wicket Keeper Count'),
                  ),
                ),
                SizedBox(
                  width: 10,
                ),
                Expanded(
                  child: TextField(
                    style: TextStyle(
                      color: isDarkMode ? Colors.grey.shade400 : Colors.black,
                    ),
                    controller: viewModel.wicketKeeperMaxC,
                    keyboardType: TextInputType.number,
                    enabled: !viewModel.disableAllFields,
                    decoration: inputDecoration(context, 'Max'),
                  ),
                )
              ],
            ),
            const SizedBox(height: 20.0),
            TextField(
              style: TextStyle(
                color: isDarkMode ? Colors.grey.shade400 : Colors.black,
              ),
              controller: viewModel.averagePlayerCostC,
              keyboardType: TextInputType.number,
              enabled: !viewModel.disableAllFields,
              decoration: inputDecoration(context, 'Average Player Cost'),
            ),
            const SizedBox(height: 20.0),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusAndTimerCard(AdminDashboardViewModel viewModel) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    return Card(
      color: isDarkMode ? Colors.grey[850] : Colors.white,
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            StatusLog(
              isMinimumReached: viewModel.isMinimumReached,
              auctionButtonState: viewModel.auctionButtonState,
            ),
            SizedBox(height: 10),
            Text(
              'Timer: ${viewModel.timerDuration - viewModel.elapsedSeconds} seconds',
              style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAuctionButton(AdminDashboardViewModel viewModel) {
    switch (viewModel.auctionButtonState) {
      case AuctionButtonState.next:
        return Row(children: [
          Expanded(
            child: getAuctionButton(
                context: context,
                text: "Get Another Player",
                onPressed: viewModel.wsGetAnotherPlayer,
                visibility: true),
          ),
          SizedBox(
            width: 10,
          ),
          getAuctionButton(
              context: context,
              text: "Stop Auction",
              onPressed: viewModel.wsStopAuction,
              visibility: true)
        ]);
      case AuctionButtonState.none:
        return getAuctionButton(
            context: context, text: "", onPressed: () {}, visibility: false);

      case AuctionButtonState.start:
        return getAuctionButton(
            context: context,
            text: "Start Auction",
            onPressed: viewModel.wsStartAuction,
            visibility: true,
            enabled: true);
      case AuctionButtonState.ongoing:
        return Row(children: [
          Expanded(
            child: getAuctionButton(
                context: context,
                text: "Last Call",
                onPressed: viewModel.wsLastCallOption,
                visibility: true,
                enabled: viewModel.isLastCallEnabledForPlayer == false
                    ? true
                    : false),
          ),
          SizedBox(
            width: 10,
          ),
          // getAuctionButton(
          //     context: context,
          //     text: "Pause Auction",
          //     onPressed: wsPauseAuction,
          //     visibility: true)
        ]);
      // return  getAuctionButton(
      //         context: context,
      //         text: "Last Call",
      //         onPressed: wsLastCallOption,
      //         visibility: true,
      //         enabled: isLastCallEnabledForPlayer == false ? true : false);
      case AuctionButtonState.playerLimitReached:
        return Row(children: [
          Expanded(
            child: getAuctionButton(
                context: context,
                text: "Get Another Player",
                onPressed: () {},
                visibility: true,
                enabled: false),
          ),
          SizedBox(
            width: 10,
          ),
          getAuctionButton(
              context: context,
              text: "Stop Auction",
              onPressed: viewModel.wsStopAuction,
              visibility: true)
        ]);
      default:
        return Container();
    }
  }

  getAuctionButton({
    required String text,
    required VoidCallback onPressed,
    bool? enabled,
    required bool visibility,
    required BuildContext context,
  }) {
    final viewModel =
        Provider.of<AdminDashboardViewModel>(context, listen: false);
    bool enabledV = enabled ?? viewModel.isMinimumReached;
    return Visibility(
      visible: visibility,
      child: ElevatedButton(
        onPressed: enabledV ? onPressed : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: enabledV ? Colors.blue : Colors.grey,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.0),
          ),
          elevation: 5,
        ),
        child: Text(
          text,
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }

  InputDecoration inputDecoration(BuildContext context, String labelText) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return InputDecoration(
      labelText: labelText,
      labelStyle: TextStyle(
        color: isDarkMode ? Colors.grey.shade400 : Colors.black,
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: isDarkMode ? Colors.blue : Colors.black,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: isDarkMode ? Colors.blue[300]! : Colors.grey,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: isDarkMode ? Colors.blueAccent : Colors.black,
          width: 2,
        ),
      ),
    );
  }

  //Clean data base popup
  Future DBCleanMessagePopup() async {
    final viewModel = Provider.of<AdminDashboardAPI>(context, listen: false);

    if (kIsWeb) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialogView(
              key: const Key("database_clean"),
              title: "Database Clean",
              message:
                  "Are you sure you wanted to delete all players Data from Database.",
              popButtonTitle: 'No',
              successButtonTitle: 'Yes',
              onPressFunction: () async {
                await viewModel.DBClean();
                context.pop();
              });
        },
      );
    } else {
      CustomBottomSheet.open(context,
          child: FunctionalSheet(
              key: const Key("database_clean"),
              message:
                  "Are you sure you wanted to delete all players Data from Database",
              buttonName: 'Yes',
              onPressButton: () async {
                await viewModel.DBClean();
                context.pop();
              }));
    }
  }
}
