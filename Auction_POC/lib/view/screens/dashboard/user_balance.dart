import 'package:flutter/material.dart';
import 'package:flutter_boilerplate/models/bid_transaction.dart';
import 'package:flutter_boilerplate/view_model/admin_dashboard_view_model.dart';
import 'package:provider/provider.dart';

class UserBalance extends StatelessWidget {
  static const String name = "userBalance";
  static const String path = "/$name";
  const UserBalance({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    final AdminDashboardViewModel viewModel =
        Provider.of<AdminDashboardViewModel>(context, listen: true);
    BidTransaction transaction = viewModel.usersListing;
    return Scaffold(
      backgroundColor: isDarkMode ? Colors.grey[850] : Colors.white,
      body: UsersDataTable(
        usersData: transaction.data?.users ?? [],
      ),
    );
  }
}

class UsersDataTable extends StatelessWidget {
  final List<Users> usersData;
  const UsersDataTable({super.key, required this.usersData});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    if (usersData.isEmpty) {
      return Center(
        child: Text(
          'No players have joined yet. Please wait for participants to join.',
          style: TextStyle(
              color: isDarkMode ? Colors.grey.shade400 : Colors.black),
        ),
      );
    }

    return SingleChildScrollView(
      scrollDirection: Axis.vertical,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(10),
            margin: EdgeInsets.only(bottom: 10),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'User Balance Information:',
                  style: TextStyle(
                      fontSize: 18.0,
                      color: Colors.black,
                      fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 10.0),
                Text(
                  'Total Users: ${usersData.length}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade800,
                  ),
                ),
              ],
            ),
          ),
          DataTable(
            dataRowMaxHeight: 60,
            columnSpacing: 10,
            columns: const <DataColumn>[
              DataColumn(
                label: Text('Name'),
              ),
              DataColumn(
                label: Text('Balance'),
              ),
            ],
            rows: List<DataRow>.generate(
              usersData.length,
              (index) {
                Users userData = usersData[index];
                return DataRow(
                  cells: <DataCell>[
                    DataCell(
                      Text(
                        userData.name ?? "",
                        style: TextStyle(
                          color: Colors.black,
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    DataCell(Text(
                      (userData.remainingBalance ?? "").toString(),
                      style: TextStyle(
                        color: Colors.black,
                      ),
                      maxLines: 3,
                    )),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
