import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_boilerplate/models/bid_transaction.dart';

class PlayersTable extends StatefulWidget {
  final StreamController dataStream;
  const PlayersTable({super.key, required this.dataStream});

  @override
  State<PlayersTable> createState() => _PlayersTableState();
}

class _PlayersTableState extends State<PlayersTable> {
  List<BidTransaction> bidTransactions = [];
  bool _hasData = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return StreamBuilder(
      stream: widget.dataStream.stream,
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return Center(
            child: Text('Error: ${snapshot.error}'),
          );
        }
        if (snapshot.hasData) {
          _hasData = true;
          var jsonDecoded = snapshot.data;
          BidTransaction tempBidTransaction =
              BidTransaction.fromJson(jsonDecoded);
          if (tempBidTransaction.action.toString() == "resetLastCallTimer") {
            bool flag = false;
            if (bidTransactions
                .map((e) => e.text?.player?.id)
                .contains(tempBidTransaction.text?.player?.id)) {
              for (var element in bidTransactions) {
                if (element.text?.higghestBid ==
                    tempBidTransaction.text?.higghestBid) {
                  flag = true;
                }
              }
            }
            if (!flag || bidTransactions.isEmpty) {
              bidTransactions.add(tempBidTransaction);
            }
          } else if (tempBidTransaction.action.toString() ==
              "auctionCompleted") {
            bidTransactions.clear();
          }
          return PlayersDataTable(bidTransactions: bidTransactions);
        }
        return _hasData
            ? PlayersDataTable(bidTransactions: bidTransactions)
            : Center(
                child: Text(
                  "No auction bets available",
                  style: TextStyle(
                      color: isDarkMode ? Colors.grey.shade400 : Colors.black),
                ),
              );
      },
    );
  }
}

class PlayersDataTable extends StatelessWidget {
  final List<BidTransaction> bidTransactions;
  const PlayersDataTable({super.key, required this.bidTransactions});

  @override
  Widget build(BuildContext context) {
    if (bidTransactions.isEmpty) {
      return Center(
        child: Text('No data available'),
      );
    }
    final latestEntry = bidTransactions.last;
    return SingleChildScrollView(
      scrollDirection: Axis.vertical,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.list_alt, color: Colors.blue.shade900, size: 24),
              SizedBox(width: 6),
              Text(
                'List of Transactions:',
                style: TextStyle(
                    fontSize: 18.0,
                    color: Colors.black,
                    fontWeight: FontWeight.bold),
              ),
            ],
          ),
          SizedBox(
            height: 12,
          ),
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(12),
            margin: EdgeInsets.only(bottom: 10),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.blue.shade300, width: 1),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.emoji_events,
                        color: Colors.amber.shade700, size: 20),
                    SizedBox(width: 6),
                    Text(
                      'Highest Bidder: ${latestEntry.text?.higghestBidder ?? "N/A"}',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade800,
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Icon(Icons.attach_money,
                        color: Colors.green.shade700, size: 20),
                    SizedBox(width: 6),
                    Text(
                      'Bid Amount: ${latestEntry.text?.higghestBid ?? "N/A"}',
                      style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w700,
                          color: Colors.black87),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Icon(Icons.person, color: Colors.black87, size: 20),
                    SizedBox(width: 6),
                    Text(
                      'Player: ${latestEntry.text?.player?.name ?? "N/A"} '
                      '(${latestEntry.text?.player?.playerType?.type ?? "Unknown"})',
                      style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87),
                    ),
                  ],
                ),
              ],
            ),
          ),
          DataTable(
            dataRowMaxHeight: 60,
            columnSpacing: 10,
            columns: const <DataColumn>[
              DataColumn(
                label: Text('Player'),
              ),
              DataColumn(
                label: Text('User'),
              ),
              DataColumn(
                label: Text('Amount'),
              ),
            ],
            rows: List<DataRow>.generate(
              bidTransactions.length,
              (index) {
                return DataRow(
                  cells: <DataCell>[
                    DataCell(
                      Text(
                        "${bidTransactions[index].text?.player?.name ?? ""} (${bidTransactions[index].text?.player?.playerType?.type ?? ""})",
                        style: TextStyle(
                          color: Colors.black,
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    DataCell(Text(
                      (bidTransactions[index].text?.higghestBidder ?? "")
                          .toString(),
                      style: TextStyle(
                        color: Colors.black,
                      ),
                      maxLines: 3,
                    )),
                    DataCell(Text(
                      (bidTransactions[index].text?.higghestBid ?? "")
                          .toString(),
                      style: TextStyle(
                        color: Colors.black,
                      ),
                    )),
                  ],
                );
              },
            ).reversed.toList(),
          ),
        ],
      ),
    );
  }
}
