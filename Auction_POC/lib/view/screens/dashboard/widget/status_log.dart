import 'package:flutter/material.dart';
import 'package:flutter_boilerplate/view_model/admin_dashboard_view_model.dart';

class StatusLog extends StatelessWidget {
  final bool isMinimumReached;
  final AuctionButtonState auctionButtonState;
  const StatusLog({super.key, required this.isMinimumReached, required this.auctionButtonState});

  @override
  Widget build(BuildContext context) {
    return isMinimumReached? Container():Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Text((auctionButtonState==AuctionButtonState.pause)? "Waiting for all the bidders...":"Waiting for all the bidders to reconnect.",
        textAlign: TextAlign.center,
        style: TextStyle(
            color: Colors.grey
        ),),
    );
  }
}
