import 'dart:async';
import 'package:flutter/material.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';

class NetworkAwareWidget extends StatefulWidget {
  final Widget onlineChild;
  final Widget offlineChild;
  final VoidCallback onReconnect;
  final VoidCallback? onDisconnect;

  const NetworkAwareWidget({
    super.key,
    this.onDisconnect,
    required this.onReconnect,
    required this.onlineChild,
    required this.offlineChild,
  });

  @override
  State<NetworkAwareWidget> createState() => _NetworkAwareWidgetState();
}

class _NetworkAwareWidgetState extends State<NetworkAwareWidget> {
  late InternetConnectionChecker internetConnectionChecker;
  Widget? latestWidget;
  StreamController<InternetConnectionStatus> streamController =
      StreamController<InternetConnectionStatus>();

  @override
  void initState() {
    super.initState();
    internetConnectionChecker = InternetConnectionChecker.instance;
    internetConnectionChecker.connectionStatus.asStream().listen(
      (istatus) {
        streamController.sink.add(istatus);
      },
    );
    internetConnectionChecker.onStatusChange
        .asBroadcastStream()
        .listen((istatus) {
      if (istatus == InternetConnectionStatus.connected) {
        widget.onReconnect();
      } else if (istatus == InternetConnectionStatus.disconnected) {
        if (widget.onDisconnect != null) {
          widget.onDisconnect!();
        }
      }
      streamController.sink.add(istatus);
    });
  }

  checkInternetStatus() async {
    bool connected = await InternetConnectionChecker.instance.hasConnection;
    if (connected) {
      setState(() {
        latestWidget = widget.onlineChild;
      });
    } else {
      setState(() {
        latestWidget = widget.offlineChild;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<InternetConnectionStatus>(
      stream: streamController.stream,
      builder: (c, s) {
        if (s.hasData) {
          if (s.data == InternetConnectionStatus.connected) {
            return widget.onlineChild;
          } else if (s.data == null) {
            return Center(child: CircularProgressIndicator());
          } else {
            return widget.offlineChild;
          }
        } else {
          return Center(child: CircularProgressIndicator());
        }
      },
    );
  }
}
