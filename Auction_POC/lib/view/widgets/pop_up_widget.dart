import 'package:flutter/material.dart';
import 'package:flutter_boilerplate/authentication/user_repository.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

class PopUpWidget extends StatefulWidget {
  const PopUpWidget({super.key, required this.text, required this.title});
  final String text;
  final String title;

  @override
  State<PopUpWidget> createState() => _PopUpWidgetState();
}

class _PopUpWidgetState extends State<PopUpWidget> {
  UserRepository userRepository = UserRepository();

  @override
  void initState() {
    userRepository = Provider.of<UserRepository>(context, listen: false);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.title),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(widget.text),
          <PERSON><PERSON><PERSON><PERSON>(height: 20),
          Elevated<PERSON><PERSON>on(
            onPressed: () async {
             context.pop();
            },
            child: Text("Back to Screen"),
          ),
        ],
      ),
    );
  }
}
