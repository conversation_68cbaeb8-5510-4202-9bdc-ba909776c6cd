class SocketEvents {
  // Connection events
  static const String disconnectUser = 'disconnectUser';
  static const String error = 'Error';

  // User events
  static const String registerUser = 'registerUser';
  static const String getAllUserDetails = 'getAllUserDetails';
  static const String liveUsers = 'liveUsers';

  // Auction events
  static const String startAuction = 'startAuction';
  static const String lastCall = 'lastCall';
  static const String resetLastCallTimer = 'resetLastCallTimer';
  static const String soldPlayer = 'Sold Player';
  static const String Success = 'Success';
  static const String bidCompleted = 'Bid completed';
  static const String playerUnsold = 'Player unsold';
  static const String maxBidReached = 'maxBidReached';
  static const String auctionCompleted = 'auctionCompleted';
  static const String minimumPlayersRequirement = 'minimumPlayersRequirement';
  static const String getNewPlayer = 'getNewPlayer';
  static const String getUserDetails = 'getUserDetails';
  static const String liveBidders = 'liveBidders';
  static const String getUpcomingPlayers = 'getUpcomingPlayers';
}
