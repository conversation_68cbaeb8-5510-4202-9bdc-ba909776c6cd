
import 'package:flutter_boilerplate/app_manager/component/responsive/responsive.dart';
import 'package:flutter/material.dart';

class ResponsiveHelperWidget extends StatelessWidget {
  final Widget? mobile;
  final Widget? desktop;

  const ResponsiveHelperWidget({super.key, this.mobile, this.desktop});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      if (Responsive.isSmallScreen(context)) {
        return mobile!;
      } else {
        return desktop!;
      }
    });
  }
}
