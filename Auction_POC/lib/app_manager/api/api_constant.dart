import 'package:flutter/foundation.dart';
import 'package:flutter_boilerplate/app_manager/constant/environment.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class ApiConstant {
  static String get baseUrl {
    if (kIsWeb) {
      return dotenv.env[Environment.WebBaseUrl] ?? '';
    } else {
      return dotenv.env[Environment.baseURL] ?? '';
    }
  }

  static const Map cancelResponse = {
    'responseCode': 0,
    'message': 'Try Again...'
  };

  static String get socketURL {
    if (kIsWeb) {
      return dotenv.env[Environment.socketWebBaseUrl] ?? "";
    } else {
      return dotenv.env[Environment.socketBaseUrl] ?? "";
    }
  }
}
