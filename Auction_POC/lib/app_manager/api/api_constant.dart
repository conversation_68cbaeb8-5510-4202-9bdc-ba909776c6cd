import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter_boilerplate/app_manager/constant/environment.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class ApiConstant {
  static String get baseUrl {
    if (kIsWeb) {
      return dotenv.env[Environment.WebBaseUrl] ?? '';
    } else {
      String rawUrl = dotenv.env[Environment.baseURL] ?? '';

      if (rawUrl.contains('********')) {
        if (kIsWeb) {
          return rawUrl; // localhost works on web
        } else if (Platform.isAndroid) {
          return rawUrl.replaceFirst('********', '********');
        } else {
          return rawUrl.replaceFirst('********', '127.0.0.1');
        }
      }

      return rawUrl;
    }
  }

  static const Map cancelResponse = {
    'responseCode': 0,
    'message': 'Try Again...'
  };

  static String get socketURL {
    if (kIsWeb) {
      return dotenv.env[Environment.socketWebBaseUrl] ?? "";
    } else {
      String rawUrl = dotenv.env[Environment.socketBaseUrl] ?? '';

      if (rawUrl.contains('********')) {
        if (kIsWeb) {
          return rawUrl; // localhost works on web
        } else if (Platform.isAndroid) {
          return rawUrl.replaceFirst('********', '********');
        } else {
          return rawUrl.replaceFirst('********', '127.0.0.1');
        }
      }
      return rawUrl;
    }
  }
}
