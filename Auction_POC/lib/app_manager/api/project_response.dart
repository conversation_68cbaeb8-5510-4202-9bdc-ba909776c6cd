import 'package:flutter_boilerplate/util/logger.dart';

import 'package:flutter_boilerplate/authentication/user.dart';

class ProjectResponse<T> {
  int? status;
  T? data;
  String? message;

  ProjectResponse({this.status, this.data, this.message});

  ProjectResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'];
    message = json['message'];
    log(" ProjectResponse values added");
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['data'] = this.data;
    data['message'] = message;
    return data;
  }
}

class AllUserResponse {
  bool? status;
  UserData? data;
  String? message;
  String? socketId;

  AllUserResponse({this.status, this.data, this.message, this.socketId});

  AllUserResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'] as bool?;
    if (json['data'] != null && json['data']['data'] != null) {
      data = UserData.fromJson(json['data']['data']);
    }
    message = json['message'];
    socketId = json['socketId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> output = <String, dynamic>{};
    output['status'] = status;

    if (data != null) {
      output['data'] = {'data': data!.toJson()};
    }

    output['message'] = message;
    output['socketId'] = socketId;
    return output;
  }

  @override
  String toString() {
    return 'AllUserResponse{status: $status, message: $message, socketId: $socketId, data: $data}';
  }
}

class LiveBiddersResponse {
  Object? status;
  UserData? data;
  int? averagePlayerCost;

  LiveBiddersResponse({this.status, this.data, this.averagePlayerCost});

  LiveBiddersResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = UserData.fromJson(json);
    averagePlayerCost = json['averagePlayerCost'];
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'data': data,
      'averagePlayerCost': averagePlayerCost,
    };
  }
}

class UserData {
  List<User>? users;
  PlayerTypeCounts? playerTypeCounts;
  AuctionData? auctionData;

  UserData({this.users, this.playerTypeCounts, this.auctionData});

  UserData.fromJson(Map<String, dynamic> json) {
    if (json['users'] != null) {
      users = <User>[];
      json['users'].forEach((v) {
        users!.add(User.fromJson(v));
      });
    }
    playerTypeCounts = json['playerTypeCounts'] != null
        ? PlayerTypeCounts.fromJson(json['playerTypeCounts'])
        : null;
    auctionData = json['auctionInfo'] != null
        ? AuctionData.fromJson(json['auctionInfo'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};

    if (users != null) {
      data['users'] = users!.map((v) => v.toJson()).toList();
    }

    if (playerTypeCounts != null) {
      data['playerTypeCounts'] = playerTypeCounts!.toJson();
    }

    if (auctionData != null) {
      data['auctionInfo'] = auctionData!.toJson();
    }

    return data;
  }
}

class PlayerTypeCounts {
  int? totalBatsman;
  int? totalBowler;
  int? totalWK;

  PlayerTypeCounts({this.totalBatsman, this.totalBowler, this.totalWK});

  PlayerTypeCounts.fromJson(Map<String, dynamic> json) {
    totalBatsman = json['totalBatsman'];
    totalBowler = json['totalBowler'];
    totalWK = json['totalWK'];
    log("Player value added");
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['totalBatsman'] = totalBatsman;
    data['totalBowler'] = totalBowler;
    data['totalWK'] = totalWK;
    return data;
  }
}

class AuctionData {
  int? totalAuctionBatsmen;
  int? totalAuctionBowlers;
  int? totalAuctionWK;
  int? userWalletBalance;

  AuctionData(
      {this.totalAuctionBatsmen,
      this.totalAuctionBowlers,
      this.totalAuctionWK,
      this.userWalletBalance});

  AuctionData.fromJson(Map<String, dynamic> json) {
    totalAuctionBatsmen = json['max_batsman'];
    totalAuctionBowlers = json['max_bowler'];
    totalAuctionWK = json['max_wicketkeeper'];
    userWalletBalance = json['assigned_amount'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['max_batsman'] = totalAuctionBatsmen;
    data['max_bowler'] = totalAuctionBowlers;
    data['max_wicketkeeper'] = totalAuctionWK;
    data['assigned_amount'] = userWalletBalance;
    return data;
  }
}

class UpcomingAuctionPlayers {
  bool? status;
  List<Player>? players;
  String? socketId;

  UpcomingAuctionPlayers({this.status, this.players, this.socketId});

  UpcomingAuctionPlayers.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    socketId = json['socketId'];
    if (json['data'] != null) {
      players = (json['data'] as List).map((v) => Player.fromJson(v)).toList();
    }
    log("Upcoming Auction Players fetched");
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'socketId': socketId,
      'text': {'data': players?.map((v) => v.toJson()).toList()},
    };
  }
}
