import 'package:flutter/material.dart';
import 'package:flutter_boilerplate/app_manager/theme/app_color.dart';

class CustomCheckBoxTheme {
  static final CheckboxThemeData primaryLight = CheckboxThemeData(
      fillColor: WidgetStateProperty.all<Color>(AppColor.primary),
      checkColor: WidgetStateProperty.all<Color>(AppColor.white));
  static final CheckboxThemeData primaryDark = CheckboxThemeData(
      fillColor: WidgetStateProperty.all<Color>(AppColor.secondary),
      checkColor: WidgetStateProperty.all<Color>(AppColor.white));
}
