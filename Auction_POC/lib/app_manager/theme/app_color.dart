import 'package:flutter/material.dart';
import 'package:flutter_boilerplate/app_manager/extension/color_extention.dart';

class AppColor {
  static Color primary = "#5277E8".toColor();
  static Color secondary = "#000000".toColor();
  static Color white = "FFFFFF".toColor();

  static Color scaffoldBackgroundColor = grey3;

  static Color grey = "#D9D9D9".toColor();
  static Color grey0 = "#E1E1E1".toColor();
  static Color grey1 = "#333333".toColor();
  static Color grey5 = "#E0E0E0".toColor();
  static Color grey2 = "#E7E7E7".toColor();
  static Color grey3 = "#F9F9F9".toColor();
  static Color grey4 = "#D7D7D7".toColor();
  static Color grey6 = "#979797".toColor();
  static Color grey7 = "#B4B4B4".toColor();
  static Color grey8 = "#F8F8F8".toColor();
  static Color greyDark = "#686F7C".toColor();

  static Color hintColor = Colors.black.withOpacity(0.25);
  static Color hintColorForDarkTheme = Colors.white.withOpacity(0.40);

  static Color error = "#D65D5D".toColor();
}
