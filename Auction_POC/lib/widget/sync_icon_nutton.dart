import 'package:flutter/material.dart';


class SyncIcon<PERSON>utton extends StatefulWidget {
  final bool isSyncing;
  final VoidCallback onPressed;

  const SyncIconButton({super.key, required this.isSyncing, required this.onPressed});

  @override
  State<SyncIconButton> createState() => _SyncIconButtonState();
}

class _SyncIconButtonState extends State<SyncIconButton> with SingleTickerProviderStateMixin {
  late final AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    ); // we'll control this later
  }

  @override
  void didUpdateWidget(SyncIconButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isSyncing) {
      _controller.repeat();
    } else {
      _controller.stop();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RotationTransition(
      turns: _controller,
      child: IconButton(
        icon: const Icon(Icons.sync),
        onPressed: widget.onPressed,
      ),
    );
  }
}
