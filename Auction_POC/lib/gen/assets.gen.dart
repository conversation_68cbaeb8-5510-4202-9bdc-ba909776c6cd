/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';

class $AssetsPngGen {
  const $AssetsPngGen();

  /// File path: assets/png/bg_wallpaper.png
  AssetGenImage get bgWallpaper =>
      const AssetGenImage('assets/png/bg_wallpaper.png');

  /// File path: assets/png/error.png
  AssetGenImage get error => const AssetGenImage('assets/png/error.png');

  /// File path: assets/png/ic_apple.png
  AssetGenImage get icApple => const AssetGenImage('assets/png/ic_apple.png');

  /// File path: assets/png/ic_facebook.png
  AssetGenImage get icFacebook =>
      const AssetGenImage('assets/png/ic_facebook.png');

  /// File path: assets/png/ic_google.png
  AssetGenImage get icGoogle => const AssetGenImage('assets/png/ic_google.png');

  /// File path: assets/png/ic_instagram.png
  AssetGenImage get icInstagram =>
      const AssetGenImage('assets/png/ic_instagram.png');

  /// File path: assets/png/ic_otp2.png
  AssetGenImage get icOtp2 => const AssetGenImage('assets/png/ic_otp2.png');

  /// File path: assets/png/ic_otp3.png
  AssetGenImage get icOtp3 => const AssetGenImage('assets/png/ic_otp3.png');

  /// File path: assets/png/ic_otp_image.png
  AssetGenImage get icOtpImage =>
      const AssetGenImage('assets/png/ic_otp_image.png');

  /// File path: assets/png/ic_twitter.png
  AssetGenImage get icTwitter =>
      const AssetGenImage('assets/png/ic_twitter.png');

  /// List of all assets
  List<AssetGenImage> get values => [
        bgWallpaper,
        error,
        icApple,
        icFacebook,
        icGoogle,
        icInstagram,
        icOtp2,
        icOtp3,
        icOtpImage,
        icTwitter
      ];
}

class $AssetsTranslationsGen {
  const $AssetsTranslationsGen();

  /// File path: assets/translations/en-US.json
  String get enUS => 'assets/translations/en-US.json';

  /// File path: assets/translations/hi-IN.json
  String get hiIN => 'assets/translations/hi-IN.json';

  /// List of all assets
  List<String> get values => [enUS, hiIN];
}

class Assets {
  Assets._();

  static const $AssetsPngGen png = $AssetsPngGen();
  static const $AssetsTranslationsGen translations = $AssetsTranslationsGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName);

  final String _assetName;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = false,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.low,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
