import 'package:flutter_boilerplate/util/logger.dart';
import 'dart:io';

import 'package:csv/csv.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boilerplate/app_manager/api/api_call.dart';
import 'package:flutter_boilerplate/app_manager/api/api_constant.dart';
import 'package:flutter_boilerplate/app_manager/constant/storage_constant.dart';
import 'package:flutter_boilerplate/app_manager/helper/local_storage.dart';
import 'package:flutter_boilerplate/authentication/user.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:universal_html/html.dart' as html;

class AdminDashboardAPI extends ChangeNotifier {
  final ApiCall _apiCall = ApiCall();
  http.Client client = http.Client();
  User? currentUser;

  Future<bool> _validateCsvFile(List<int> bytes, String fileName) async {
    try {
      final sizeInMB = bytes.length / (1024 * 1024);
      if (sizeInMB > 10) {
        log("File validation failed: File size ${sizeInMB.toStringAsFixed(2)}MB exceeds 10MB limit");
        return false;
      }

      final content = String.fromCharCodes(bytes);
      if (content.isEmpty) {
        log("File validation failed: File is empty");
        return false;
      }

      try {
        final lines = content.split('\n').take(3).join('\n');
        const CsvToListConverter().convert(lines);
        log("File validation passed: CSV format is valid");
        return true;
      } catch (e) {
        log("File validation failed: Invalid CSV format - $e");
        return false;
      }
    } catch (e) {
      log("File validation error: $e");
      return false;
    }
  }

  Future<void> pickFile() async {
    log("Starting file picker process");
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        allowedExtensions: ['csv'],
        type: FileType.custom,
        allowMultiple: false,
        withData: true,
      );

      if (result != null && result.files.isNotEmpty) {
        log("File selected: ${result.files.first.name}");
        final file = result.files.first;
        final bytes = file.bytes;

        if (bytes == null) {
          log("Error: File path is null");
          Fluttertoast.showToast(msg: "Invalid file selected");
          return;
        }

        if (!await _validateCsvFile(bytes, file.name)) {
          Fluttertoast.showToast(
              msg: "Invalid CSV file. Please check the file format and size");
          return;
        }
        log("Starting file upload process");
        await uploadFile(file);
      } else {
        log("File selection cancelled by user");
        Fluttertoast.showToast(msg: "No file selected");
      }
    } catch (e) {
      log("Error in file picking process: $e");
      Fluttertoast.showToast(msg: "Error selecting file: Please try again");
    }
  }

  Future<void> uploadFile(PlatformFile file) async {
    log("Starting file upload for: ${file.name}");
    try {
      String? token =
          await LocalStorage.getString(key: StorageConstant.userToken);
      if (token == null) {
        log("Error: Authentication token is null");
        Fluttertoast.showToast(msg: "Authentication error");
        return;
      }

      var uri = Uri.parse("${ApiConstant.baseUrl}user/import-csv");
      var request = http.MultipartRequest('POST', uri);
      request.headers.addAll({
        'Authorization': token,
        'Content-Type': 'multipart/form-data',
      });

      if (file.bytes != null) {
        request.files.add(http.MultipartFile.fromBytes(
          'file',
          file.bytes!,
          filename: file.name,
        ));
      } else {
        Fluttertoast.showToast(msg: "Invalid file data");
        return;
      }

      final streamedResponse = await request.send();
      var response = await http.Response.fromStream(streamedResponse);
      log("Upload response status code: ${response.statusCode}");
      log("Upload response body: ${response.body}");
      if (response.statusCode == 200) {
        log('File uploaded successfully: ${file.name}');
        Fluttertoast.showToast(
          msg: 'File uploaded successfully',
          toastLength: Toast.LENGTH_LONG,
        );
      } else {
        log('File upload failed with status code ${response.statusCode}: ${response.body}');
        Fluttertoast.showToast(
          msg: 'File upload failed. Please try again',
          toastLength: Toast.LENGTH_LONG,
        );
      }
    } catch (e) {
      log('Error uploading file: $e');
      Fluttertoast.showToast(
        msg: 'Error uploading file. Please try again',
        toastLength: Toast.LENGTH_LONG,
      );
    }
  }

  Future<bool> _requestStoragePermission() async {
    if (Platform.isAndroid) {
      var storageStatus = await Permission.storage.status;
      var manageExternalStatus = await Permission.manageExternalStorage.status;

      log("Initial storage status: $storageStatus");
      log("Initial manage external status: $manageExternalStatus");

      if (storageStatus.isDenied) {
        storageStatus = await Permission.storage.request();
        log("After request - storage status: $storageStatus");
      }

      if (manageExternalStatus.isDenied) {
        manageExternalStatus = await Permission.manageExternalStorage.request();
        log("After request - manage external status: $manageExternalStatus");
      }

      if (storageStatus.isPermanentlyDenied ||
          manageExternalStatus.isPermanentlyDenied) {
        Fluttertoast.showToast(
            msg: "Please enable storage permission from settings",
            toastLength: Toast.LENGTH_LONG);
        await openAppSettings();
        return false;
      }

      return storageStatus.isGranted || manageExternalStatus.isGranted;
    }
    return true;
  }

  Future<void> downloadFile() async {
    print("here");
    try {
      if (!kIsWeb) {
        bool hasPermission = await _requestStoragePermission();
        log("Permission check result: $hasPermission");

        if (!hasPermission) {
          Fluttertoast.showToast(
              msg:
                  "Storage permission is required. Please grant permission and try again.",
              toastLength: Toast.LENGTH_LONG);
          return;
        }
      }
      String? token =
          await LocalStorage.getString(key: StorageConstant.userToken);
      print(token);
      final url = Uri.parse("${ApiConstant.baseUrl}user/export-csv");
      final response = await http.get(
        url,
        headers: {
          'Authorization': token ?? '',
          'Accept': 'text/csv',
        },
      );
      print(response.statusCode);

      if (response.statusCode == 200) {
        final csvData = response.body;

        if (csvData.isEmpty) {
          Fluttertoast.showToast(msg: "No data available to export");
          return;
        }

        if (kIsWeb) {
          final bytes = response.bodyBytes;
          final blob = html.Blob([bytes]);
          final url = html.Url.createObjectUrlFromBlob(blob);
          final timestamp = DateTime.now().millisecondsSinceEpoch;

          final anchor = html.AnchorElement(href: url)
            ..setAttribute("download", "player_data_$timestamp.csv")
            ..style.display = 'none';

          html.document.body!.children.add(anchor);
          anchor.click();

          html.document.body!.children.remove(anchor);
          html.Url.revokeObjectUrl(url);

          Fluttertoast.showToast(
            msg: "File downloaded successfully",
            toastLength: Toast.LENGTH_LONG,
          );
        } else {
          if (Platform.isAndroid) {
            Directory? downloadsDir = Directory('/storage/emulated/0/Download');
            if (downloadsDir.existsSync()) {
              Directory newFolder = Directory('${downloadsDir.path}/Auction');
              if (!newFolder.existsSync()) {
                newFolder.createSync();
              }

              final timestamp = DateTime.now().millisecondsSinceEpoch;
              final fileName = 'player_data_$timestamp.csv';
              final filePath = '${newFolder.path}/$fileName';
              final file = File(filePath);

              await file.writeAsString(csvData);

              Fluttertoast.showToast(
                msg: "File downloaded successfully to Downloads/Auction folder",
                toastLength: Toast.LENGTH_LONG,
              );
            } else {
              throw Exception('Downloads directory not found');
            }
          } else if (Platform.isIOS) {
            final directory = await getApplicationDocumentsDirectory();
            final timestamp = DateTime.now().millisecondsSinceEpoch;
            final fileName = 'player_data_$timestamp.csv';
            final filePath = '${directory.path}/$fileName';
            final file = File(filePath);

            await file.writeAsString(csvData);

            Fluttertoast.showToast(
              msg: "File downloaded successfully to Downloads folder",
              toastLength: Toast.LENGTH_LONG,
            );
          }
        }
      } else {
        log("Server response: ${response.statusCode} - ${response.body}");
        Fluttertoast.showToast(
            msg:
                "Failed to download file: Server error ${response.statusCode}");
      }
    } catch (e) {
      log("Error downloading CSV: $e");
      Fluttertoast.showToast(
        msg: "Failed to download CSV. Please try again.",
        toastLength: Toast.LENGTH_LONG,
      );
    }
  }

  Future<void> DBClean() async {
    log("cleaning DB");
    try {
      String? token =
          await LocalStorage.getString(key: StorageConstant.userToken);
      log("current user is $token");
      var response = await _apiCall.call(
          url: "db/reset",
          client: client,
          apiCallType: ApiCallType.post(body: {}),
          useThisToken: token,
          token: true);
      log(response);
      Fluttertoast.showToast(
        msg: 'Database Reset Successfully',
        toastLength: Toast.LENGTH_LONG,
      );
    } catch (e) {
      log("error is $e");
      notifyListeners();
    }
  }
}
