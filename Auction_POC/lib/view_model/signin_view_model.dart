import 'package:flutter_boilerplate/util/logger.dart';

import 'package:flutter/material.dart';
import 'package:flutter_boilerplate/app_manager/api/api_call.dart';
import 'package:flutter_boilerplate/app_manager/api/project_response.dart';
import 'package:flutter_boilerplate/app_manager/enum/button_status.dart';
import 'package:flutter_boilerplate/app_manager/helper/show_toast.dart';
import 'package:flutter_boilerplate/authentication/user.dart';
import 'package:http/http.dart' as http;

class SignInViewModel extends ChangeNotifier {
  var isPhoneNumberEntered = false;
  var isValidNumber = false;
  http.Client client = http.Client();

  final TextEditingController firstNameC = TextEditingController();
  final TextEditingController phoneC = TextEditingController();
  final TextEditingController passwordC = TextEditingController();

  final ApiCall _apiCall = ApiCall();

  ButtonStatus _loginStatus = ButtonStatus.initial;

  ButtonStatus get loginStatus => _loginStatus;

  set loginStatus(ButtonStatus val) {
    _loginStatus = val;
    notifyListeners();
  }

  set setPhoneNumberValidated(bool value) {
    isPhoneNumberEntered = value;
    notifyListeners();
  }

  set setIsValidNumber(bool value) {
    isValidNumber = value;
    notifyListeners();
  }

  Future<User?> signIn({
    required String phone,
    required String password,
  }) async {
    try {
      loginStatus = ButtonStatus.hit;
      var body = {
        "phone": phone,
        "password": password,
      };
      var response = await _apiCall.call(
          url: "auth/signin",
          client: client,
          apiCallType: ApiCallType.post(body: body));
      ProjectResponse projectResponse = ProjectResponse.fromJson(response);
      loginStatus = ButtonStatus.complete;
      if (projectResponse.status == 1) {
        loginStatus = ButtonStatus.complete;
        return User.fromJson(projectResponse.data);
        // }
      } else {
        showToast(
          projectResponse.message ?? "",
        );
        if (projectResponse.data != null) {
          // print(projectResponse.data['role'].toString());
        }
      }
    } catch (e) {
      log("error4 is $e");
      // showToast(
      //   "User not found!",
      // );
      loginStatus = ButtonStatus.error;
    }
    return null;
  }
}
