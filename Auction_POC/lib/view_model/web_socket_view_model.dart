import 'package:flutter/material.dart';
import 'package:flutter_boilerplate/app_manager/api/api_constant.dart';
import 'package:flutter_boilerplate/app_manager/api/project_response.dart';
import 'package:flutter_boilerplate/app_manager/constant/socket_events.dart';
import 'package:flutter_boilerplate/app_manager/helper/navigation/navigation_helper.dart';
import 'package:flutter_boilerplate/app_manager/helper/show_toast.dart';
import 'package:flutter_boilerplate/app_manager/service/navigation_service.dart';
import 'package:flutter_boilerplate/authentication/user_repository.dart';
import 'package:flutter_boilerplate/models/auction.dart';
import 'package:flutter_boilerplate/models/bid_transaction.dart';
import 'package:flutter_boilerplate/util/logger.dart';
import 'package:flutter_boilerplate/view/screens/auction/auction_screen.dart';
import 'package:flutter_boilerplate/view/widgets/pop_up_widget.dart';
import 'package:socket_io_client/socket_io_client.dart' as IO;

enum AuctionState {
  initial,
  registerUser,
  startAuction,
  getNewPlayer,
  resetLastCallTimer,
  success,
  soldPlayer,
  playerUnsold,
  error,
  lastCall,
}

class WebSocketViewModel extends ChangeNotifier {
  IO.Socket? socket;
  static ValueNotifier<AuctionState> state =
      ValueNotifier(AuctionState.initial);
  LiveBiddersResponse liveBidders = LiveBiddersResponse();
  UpcomingAuctionPlayers upcomingPlayers = UpcomingAuctionPlayers();
  UserRepository? userRepository;
  bool isLastCallTimerDisplay = false;
  bool resetLastCallTimer = false;

  bool _onAuctionPage = false;
  bool get onAuctionPage => _onAuctionPage;
  set onAuctionPage(bool val) {
    _onAuctionPage = val;
    safeNotifyListeners();
  }

  int _elapsedSeconds = 0;
  int get elapsedSeconds => _elapsedSeconds;
  set elapsedSeconds(int val) {
    _elapsedSeconds = val;
    notifyListeners();
  }

  int get timerDuration => 20;

  Auction auction = Auction(
      playerName: '',
      playerId: '',
      playerType: '',
      basePrice: 0,
      leadingBidder: '',
      leadingBid: 0,
      leadingBidderId: '');

  String socketId = '';
  int maxPlayerLimit = 0;
  bool _pauseActivated = false;
  bool get pauseActivated => _pauseActivated;

  bool _buttonEnable = true;
  bool get buttonEnable => _buttonEnable;
  set buttonEnable(bool val) {
    _buttonEnable = val;
    notifyListeners();
  }

  BidTransaction _transaction = BidTransaction();
  BidTransaction get transaction => _transaction;
  set transaction(BidTransaction val) {
    _transaction = val;
    notifyListeners();
  }

  WebSocketViewModel({required this.userRepository});

  @override
  void dispose() {
    disconnectClient();
    super.dispose();
  }

  void safeNotifyListeners() {
    try {
      notifyListeners();
    } catch (e) {
      log('Error in notifyListeners: $e', name: 'SocketIO');
    }
  }

  void wsConnection() {
    log('Initiating Socket.IO connection', name: 'SocketIO');

    if (socket != null) {
      socket?.disconnect();
      socket?.dispose();
      socket = null;
    }

    socket = IO.io(
        ApiConstant.socketURL,
        IO.OptionBuilder()
            .setTransports(['websocket'])
            .disableAutoConnect()
            .enableReconnection()
            .setReconnectionDelay(1000)
            .setReconnectionAttempts(5)
            .build());

    _setupSocketBasicEvents();
    _setupSocketListeners();

    socket?.connect();
  }

  void _setupSocketBasicEvents() {
    socket?.onConnect((_) {
      log('Socket.IO connection established', name: 'SocketIO');
      socketId = socket?.id ?? '';
      log('Socket ID: $socketId', name: 'SocketIO');
    });

    socket?.onConnectError((data) {
      log('Socket.IO connection error: $data', name: 'SocketIO');
    });

    socket?.onDisconnect((_) {
      log('Socket.IO disconnected', name: 'SocketIO');
    });

    socket?.onError((error) {
      log('Socket.IO error: $error', name: 'SocketIO');
    });

    socket?.onAny((event, data) {
      log("Received $event event", name: 'SocketIO');
    });
  }

  void registerUserOnSocket(String token) {
    log('Registering user on Socket.IO with token', name: 'SocketIO');

    if (socket == null || !socket!.connected) {
      log('Socket not connected, attempting to connect first',
          name: 'SocketIO');
      wsConnection();

      _retryWithDelay(() => _registerUser(token),
          retryInterval: const Duration(seconds: 2), maxRetries: 2);
    } else {
      _registerUser(token);
    }
  }

  void _registerUser(String token) {
    log('Sending registerUser event with token', name: 'SocketIO');
    socket?.emit(SocketEvents.registerUser,
        {'action': SocketEvents.registerUser, 'jwt': token});
  }

  void _retryWithDelay(Function operation,
      {required Duration retryInterval,
      int maxRetries = 1,
      int currentRetry = 0}) {
    Future.delayed(retryInterval, () {
      if (socket?.connected == true) {
        operation();
      } else if (currentRetry < maxRetries) {
        log('Socket not connected after delay, retrying (${currentRetry + 1}/$maxRetries)',
            name: 'SocketIO');
        _retryWithDelay(operation,
            retryInterval: retryInterval,
            maxRetries: maxRetries,
            currentRetry: currentRetry + 1);
      } else {
        log('Max retries reached, operation failed', name: 'SocketIO');
      }
    });
  }

  Future<void> disconnectClient() async {
    log('Disconnecting Socket.IO client', name: 'SocketIO');

    bool wasOnAuctionPage = _onAuctionPage;
    _onAuctionPage = false;

    if (socket != null) {
      try {
        _resetState();

        socket?.emit(SocketEvents.disconnectUser,
            {'action': SocketEvents.disconnectUser});

        await Future.delayed(const Duration(milliseconds: 300));

        socket?.disconnect();
        socket?.dispose();
        socket = null;
        socketId = '';

        log('Socket.IO client successfully disconnected', name: 'SocketIO');
      } catch (e) {
        log('Error disconnecting socket: $e', name: 'SocketIO', error: e);
      }
    } else {
      log('Socket already null, no need to disconnect', name: 'SocketIO');
    }

    if (wasOnAuctionPage) {
      safeNotifyListeners();
    }
  }

  void _resetState() {
    resetLastCallTimer = false;
    isLastCallTimerDisplay = false;
    elapsedSeconds = 0;

    auction = Auction(
        playerName: '',
        playerId: '',
        playerType: '',
        basePrice: 0,
        leadingBidder: '',
        leadingBidderId: '',
        leadingBid: 0);
  }

  void _setupSocketListeners() {
    log('Setting up Socket.IO event listeners', name: 'SocketIO');

    _removeExistingListeners();

    socket?.on(SocketEvents.error, _handleErrorEvent);

    _registerEventHandlers();
  }

  void _removeExistingListeners() {
    socket?.off(SocketEvents.error);
    socket?.off(SocketEvents.registerUser);
    socket?.off(SocketEvents.startAuction);
    socket?.off(SocketEvents.getNewPlayer);
    socket?.off(SocketEvents.resetLastCallTimer);
    socket?.off(SocketEvents.getUserDetails);
    socket?.off(SocketEvents.lastCall);
    socket?.off(SocketEvents.Success);
    socket?.off(SocketEvents.soldPlayer);
    socket?.off(SocketEvents.playerUnsold);
    socket?.off(SocketEvents.liveBidders);
    socket?.off(SocketEvents.getUpcomingPlayers);
    socket?.off(SocketEvents.auctionCompleted);
  }

  void _handleErrorEvent(dynamic data) {
    log("Error event data: ${data.toString()}", name: 'SocketIO');
    state.value = AuctionState.error;
    log('Socket.IO Error: ${data['text']}',
        name: 'SocketIO', error: data['text']);
    showToast(data['text'] ?? "Something went wrong");
  }

  void _registerEventHandlers() {
    socket?.on(SocketEvents.registerUser, _handleRegisterUserEvent);
    socket?.on(SocketEvents.startAuction, _handleStartAuctionEvent);
    socket?.on(SocketEvents.getNewPlayer, _handleGetNewPlayerEvent);
    socket?.on(SocketEvents.resetLastCallTimer, _handleResetLastCallTimerEvent);
    socket?.on(SocketEvents.lastCall, _handleLastCallEvent);
    socket?.on(SocketEvents.Success, _handleSuccessEvent);
    socket?.on(SocketEvents.soldPlayer, _handleSoldPlayerEvent);
    socket?.on(SocketEvents.playerUnsold, _handlePlayerUnsoldEvent);
    socket?.on(SocketEvents.liveBidders, _handleLiveBiddersEvent);
    socket?.on(SocketEvents.getUpcomingPlayers, _handleGetUpcomingPlayersEvent);
    socket?.on(SocketEvents.auctionCompleted, _handleAuctionCompletedEvent);
    socket?.on(SocketEvents.getUserDetails, _handleGetUserDetailsEvent);
  }

  void _handleRegisterUserEvent(dynamic data) {
    try {
      isLastCallTimerDisplay = false;
      state.value = AuctionState.registerUser;

      if (data is Map<String, dynamic>) {
        transaction = BidTransaction.fromJson(data);
        _pauseActivated = transaction.text?.pauseActivated ?? false;
        buttonEnable = transaction.text?.biddable ?? true;
        resetLastCallTimer = false;

        if (data['text'] == 'User authorized') {
          socketId = data['socketId'] ?? socket?.id ?? '';
          log('User registered successfully with socketId: $socketId',
              name: 'SocketIO');
        }
      } else {
        log('Invalid data format for registerUser: $data', name: 'SocketIO');
      }
      notifyListeners();
    } catch (e, stackTrace) {
      log('Error processing registerUser: $e',
          name: 'SocketIO', error: e, stackTrace: stackTrace);
    }
  }

  void _handleStartAuctionEvent(dynamic data) {
    log("startAuction event data: ${data.toString()}", name: 'SocketIO');

    try {
      isLastCallTimerDisplay = false;
      state.value = AuctionState.startAuction;

      if (data is Map<String, dynamic>) {
        Map<String, dynamic> structuredData = _structureAuctionData(data);
        transaction = BidTransaction.fromJson(structuredData);
        _pauseActivated = transaction.text?.pauseActivated ?? false;
        buttonEnable = transaction.text?.biddable ?? true;
        resetLastCallTimer = false;

        auction = _createAuctionFromMessage(structuredData);
        _updateUserBalances(data);

        log('Auction started for player: ${auction.playerName}',
            name: 'SocketIO');
      } else {
        log('Invalid data format for startAuction: $data', name: 'SocketIO');
      }
      notifyListeners();
    } catch (e, stackTrace) {
      log('Error processing startAuction: $e',
          name: 'SocketIO', error: e, stackTrace: stackTrace);
    }
  }

  Map<String, dynamic> _structureAuctionData(Map<String, dynamic> data) {
    return {
      'text': {
        'player': data['player'],
        'timerDuration': data['timerDuration'],
        'batterMax': data['batterMax'],
        'wicketKeeperMax': data['wicketKeeperMax'],
        'bowlerMax': data['bowlerMax'],
        'biddable': true,
        'isBelowAverage': data['averagePlayerCost'] != null &&
            data['player'] != null &&
            data['player']['base_price'] != null &&
            data['player']['base_price'] < data['averagePlayerCost']
      }
    };
  }

  void _updateUserBalances(Map<String, dynamic> data) {
    log("Current user before update: ${userRepository?.currentUser?.toJson()}",
        name: 'SocketIO');

    if (userRepository != null) {
      int? newBalance = data['amount'] as int?;
      int? newBonusBalance = data['bonusBalance'] as int?;

      userRepository!.updateUserBalances(
          remainingBalance: newBalance, bonusBalance: newBonusBalance);

      log("Current user after update: ${userRepository?.currentUser?.toJson()}",
          name: 'SocketIO');
    } else {
      log("UserRepository is null, cannot update user data", name: 'SocketIO');
    }
  }

  void _handleGetNewPlayerEvent(dynamic data) {
    log("getNewPlayer event data: ${data.toString()}", name: 'SocketIO');

    try {
      isLastCallTimerDisplay = false;
      state.value = AuctionState.getNewPlayer;

      if (data is Map<String, dynamic>) {
        transaction = BidTransaction.fromJson(data);
        _pauseActivated = transaction.text?.pauseActivated ?? false;
        buttonEnable = transaction.text?.biddable ?? true;
        resetLastCallTimer = false;

        auction = _createAuctionFromMessage(data);
        maxPlayerLimit = _calculateMaxPlayerLimit(data);

        log('New player in auction: ${auction.playerName} (${auction.playerType})',
            name: 'SocketIO');

        if (!onAuctionPage) {
          NavigationHelper.pushNamed(
              NavigationService.context!, AuctionScreen.name);
        }
      } else {
        log('Invalid data format for getNewPlayer: $data', name: 'SocketIO');
      }
      notifyListeners();
    } catch (e, stackTrace) {
      log('Error processing getNewPlayer: $e',
          name: 'SocketIO', error: e, stackTrace: stackTrace);
    }
  }

  void _handleGetUserDetailsEvent(dynamic data) {
    log("Received getUserDetails event with data: ${data.toString()}",
        name: 'SocketIO');

    try {
      if (data is Map<String, dynamic> &&
          data.containsKey('status') &&
          data['status'] == true &&
          data.containsKey('data')) {
        Map<String, dynamic> userData = data['data'];

        if (userRepository != null) {
          userRepository!.updateUserFromSocketData(userData);
          log("UserRepository updated with getUserDetails data",
              name: 'SocketIO');
        } else {
          log("UserRepository is null, cannot update user data from getUserDetails",
              name: 'SocketIO');
        }
      } else {
        log("Invalid getUserDetails data format or status is not true",
            name: 'SocketIO');
      }
    } catch (e, stackTrace) {
      log('Error processing getUserDetails: $e',
          name: 'SocketIO', error: e, stackTrace: stackTrace);
    }
  }

  void _handleResetLastCallTimerEvent(dynamic data) {
    log("resetLastCallTimer event data: ${data.toString()}", name: 'SocketIO');

    try {
      isLastCallTimerDisplay = false;
      state.value = AuctionState.resetLastCallTimer;

      if (data is Map<String, dynamic>) {
        transaction = BidTransaction.fromJson(data);
        _pauseActivated = transaction.text?.pauseActivated ?? false;
        buttonEnable = transaction.text?.biddable ?? true;
        resetLastCallTimer = true;

        _updateBidInfo(data);

        log('Timer reset - Current bid: ${auction.leadingBid} by ${auction.leadingBidder}',
            name: 'SocketIO');
      } else {
        log('Invalid data format for resetLastCallTimer: $data',
            name: 'SocketIO');
      }
      notifyListeners();
    } catch (e, stackTrace) {
      log('Error processing resetLastCallTimer: $e',
          name: 'SocketIO', error: e, stackTrace: stackTrace);
    }
  }

  void _updateBidInfo(Map<String, dynamic> data) {
    if (data['text'] is Map) {
      auction.leadingBid = data['text']['higghestBid'] ?? auction.leadingBid;
      auction.leadingBidder =
          data['text']['higghestBidder'] ?? auction.leadingBidder;
      auction.leadingBidderId =
          data['text']['higghestBidderId'] ?? auction.leadingBidderId;
    }
  }

  void _handleLastCallEvent(dynamic data) {
    log("lastCall event data: ${data.toString()}", name: 'SocketIO');

    try {
      state.value = AuctionState.lastCall;
      isLastCallTimerDisplay = true;

      if (data is Map<String, dynamic>) {
        transaction = BidTransaction.fromJson(data);
        _pauseActivated = transaction.text?.pauseActivated ?? false;
        buttonEnable = transaction.text?.biddable ?? true;
        resetLastCallTimer = false;
      } else {
        log('Invalid data format for lastCall: $data', name: 'SocketIO');
      }
      notifyListeners();
    } catch (e, stackTrace) {
      log('Error processing lastCall: $e',
          name: 'SocketIO', error: e, stackTrace: stackTrace);
    }
  }

  void _handleSuccessEvent(dynamic data) {
    log("Success event data: ${data.toString()}", name: 'SocketIO');

    try {
      isLastCallTimerDisplay = false;
      state.value = AuctionState.success;

      if (data is Map<String, dynamic>) {
        transaction = BidTransaction.fromJson(data);
        _pauseActivated = transaction.text?.pauseActivated ?? false;
        buttonEnable = transaction.text?.biddable ?? true;
        resetLastCallTimer = false;

        log('Bid successful', name: 'SocketIO');
        _showActionDialog(data);
      } else {
        log('Invalid data format for Success: $data', name: 'SocketIO');
      }
      notifyListeners();
    } catch (e, stackTrace) {
      log('Error processing Success: $e',
          name: 'SocketIO', error: e, stackTrace: stackTrace);
    }
  }

  void _handleSoldPlayerEvent(dynamic data) {
    log("Sold Player event data: ${data.toString()}", name: 'SocketIO');

    try {
      isLastCallTimerDisplay = false;
      state.value = AuctionState.soldPlayer;

      if (data is Map<String, dynamic>) {
        transaction = BidTransaction.fromJson(data);
        _pauseActivated = transaction.text?.pauseActivated ?? false;
        buttonEnable = transaction.text?.biddable ?? true;
        resetLastCallTimer = false;

        log('Player sold: ${auction.playerName} for ${auction.leadingBid}',
            name: 'SocketIO');
        _showActionDialog(data);
      } else {
        log('Invalid data format for Sold Player: $data', name: 'SocketIO');
      }
      notifyListeners();
    } catch (e, stackTrace) {
      log('Error processing Sold Player: $e',
          name: 'SocketIO', error: e, stackTrace: stackTrace);
    }
  }

  void _handlePlayerUnsoldEvent(dynamic data) {
    log("Player unsold event data: ${data.toString()}", name: 'SocketIO');

    try {
      isLastCallTimerDisplay = false;
      state.value = AuctionState.playerUnsold;

      if (data is Map<String, dynamic>) {
        transaction = BidTransaction.fromJson(data);
        _pauseActivated = transaction.text?.pauseActivated ?? false;
        buttonEnable = transaction.text?.biddable ?? true;
        resetLastCallTimer = false;

        if (data['text'] is Map && data['text']['name'] != null) {
          final playerName = data['text']['name'];
          log('Player unsold: $playerName', name: 'SocketIO');
          showToast('Player $playerName is unsold');
        }
      } else {
        log('Invalid data format for Player unsold: $data', name: 'SocketIO');
      }
      notifyListeners();
    } catch (e, stackTrace) {
      log('Error processing Player unsold: $e',
          name: 'SocketIO', error: e, stackTrace: stackTrace);
    }
  }

  void _handleLiveBiddersEvent(dynamic data) {
    log("liveBidders event data: ${data.toString()}", name: 'SocketIO');

    try {
      liveBidders = LiveBiddersResponse.fromJson(data);
      notifyListeners();
    } catch (e, stackTrace) {
      log('Error processing liveBidders: $e',
          name: 'SocketIO', error: e, stackTrace: stackTrace);
    }
  }

  void _handleGetUpcomingPlayersEvent(dynamic data) {
    log("getUpcomingPlayers event data: ${data.toString()}", name: 'SocketIO');

    try {
      if (data is Map<String, dynamic>) {
        upcomingPlayers = UpcomingAuctionPlayers.fromJson(data);
        notifyListeners();
      }
    } catch (e, stackTrace) {
      log('Error processing getUpcomingPlayers: $e',
          name: 'SocketIO', error: e, stackTrace: stackTrace);
    }
  }

  void _handleAuctionCompletedEvent(dynamic data) {
    log("auctionCompleted event data: ${data.toString()}", name: 'SocketIO');

    try {
      if (data is Map<String, dynamic>) {
        upcomingPlayers = UpcomingAuctionPlayers.fromJson(data);
        notifyListeners();
      }
    } catch (e, stackTrace) {
      log('Error processing auctionCompleted: $e',
          name: 'SocketIO', error: e, stackTrace: stackTrace);
    }
  }

  Auction _createAuctionFromMessage(Map<String, dynamic> message) {
    final player = message['text']?['player'];

    if (player == null) {
      log('Warning: Player data is missing in message', name: 'SocketIO');
      return Auction(
        playerName: 'Unknown',
        playerId: '',
        playerType: '',
        basePrice: 0,
        leadingBidder: '',
        leadingBidderId: '',
        leadingBid: 0,
      );
    }

    return Auction(
      playerName: player['name'] ?? 'Unknown',
      playerId: player['id'] ?? '',
      playerType: player['player_type'] != null
          ? player['player_type']['type'] ?? ''
          : '',
      basePrice: player['base_price'] ?? 0,
      leadingBidder: '',
      leadingBidderId: '',
      leadingBid: 0,
    );
  }

  int _calculateMaxPlayerLimit(Map<String, dynamic> message) {
    final text = message['text'];
    if (text == null || text is! Map<String, dynamic>) {
      log('Warning: Text data is missing or invalid in message',
          name: 'SocketIO');
      return 0;
    }

    int batter = text['batter'] is int ? text['batter'] : 0;
    int wicketKeeper = text['wicketKeeper'] is int ? text['wicketKeeper'] : 0;
    int bowler = text['bowler'] is int ? text['bowler'] : 0;

    return batter + wicketKeeper + bowler;
  }

  void _showActionDialog(Map<String, dynamic> message) {
    try {
      if (NavigationService.context == null) {
        log('Navigation context is null, cannot show dialog', name: 'SocketIO');
        return;
      }

      String text = '';
      String title = '';

      if (message['text'] is Map && message['text']['message'] != null) {
        text = message['text']['message'].toString();
      }

      if (message['action'] != null) {
        title = message['action'].toString();
      }

      showDialog(
        barrierDismissible: false,
        context: NavigationService.context!,
        builder: (BuildContext context) {
          return PopUpWidget(text: text, title: title);
        },
      );
    } catch (e, stackTrace) {
      log('Error showing action dialog: $e',
          name: 'SocketIO', error: e, stackTrace: stackTrace);
    }
  }

  void _executeSocketOperation(
      String eventName, Map<String, dynamic> payload, String token) {
    try {
      log('Requesting $eventName', name: 'SocketIO');

      if (socket == null || !socket!.connected) {
        log('Socket not connected, attempting to connect first',
            name: 'SocketIO');
        wsConnection();

        _retryWithDelay(() => socket?.emit(eventName, payload),
            retryInterval: const Duration(seconds: 1), maxRetries: 1);
      } else {
        socket?.emit(eventName, payload);
        log('$eventName request sent successfully');
      }
    } catch (e, stackTrace) {
      log('Error executing $eventName: $e',
          name: 'SocketIO', error: e, stackTrace: stackTrace);
    }
  }

  void registerUser(String token) {
    registerUserOnSocket(token);
  }

  void getUpcomingPlayers(String token) {
    _executeSocketOperation(SocketEvents.getUpcomingPlayers,
        {'action': SocketEvents.getUpcomingPlayers, 'jwt': token}, token);
  }

  void getUserDetails(String token) {
    _executeSocketOperation(SocketEvents.getUserDetails,
        {'action': SocketEvents.getUserDetails, 'jwt': token}, token);
  }

  void addBid(String token, String playerId, int bid) {
    _executeSocketOperation(
        'addBid',
        {'action': 'addBid', 'jwt': token, 'playerId': playerId, 'bid': bid},
        token);
  }

  void getLiveBidders(String token) {
    _executeSocketOperation(SocketEvents.liveBidders,
        {'action': SocketEvents.liveBidders, 'jwt': token}, token);
  }

  void getAuctionStatus(String token) {
    _executeSocketOperation(
        'auctionStatus', {'action': 'auctionStatus', 'jwt': token}, token);
  }
}
