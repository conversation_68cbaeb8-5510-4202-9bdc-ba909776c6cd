import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boilerplate/app_manager/api/api_constant.dart';
import 'package:flutter_boilerplate/app_manager/api/project_response.dart';
import 'package:flutter_boilerplate/app_manager/constant/socket_events.dart';
import 'package:flutter_boilerplate/app_manager/helper/show_toast.dart';
import 'package:flutter_boilerplate/models/admin_playerResponce.dart';
import 'package:flutter_boilerplate/models/bid_transaction.dart';
import 'package:flutter_boilerplate/util/logger.dart';
import 'package:socket_io_client/socket_io_client.dart' as IO;
import '../authentication/user_repository.dart';

enum AuctionButtonState {
  none,
  pause,
  resume,
  start,
  next,
  playerLimitReached,
  ongoing
}

class AdminDashboardViewModel extends ChangeNotifier {
  final StreamController dataStream = StreamController.broadcast();

  IO.Socket? socket;

  UserRepository? userRepository;

  BidTransaction _usersListing = BidTransaction();
  BidTransaction get usersListing => _usersListing;
  set usersListing(BidTransaction val) {
    _usersListing = val;
    notifyListeners();
  }



  List<String> _eventsToSync = [];
  List<String> get eventsToSync => List.unmodifiable(_eventsToSync);
  bool get hasEventsToSync => _eventsToSync.isNotEmpty;
  void addEventToSync(String event) {
    if (!_eventsToSync.contains(event)) {
      _eventsToSync.add(event);
      notifyListeners();
    }
  }


  void removeEventToSync(String event) {
    if (_eventsToSync.contains(event)) {
      _eventsToSync.remove(event);
      notifyListeners();
    }
  }

  void removeAllEventToSync() {
    _eventsToSync = [];
    notifyListeners();
  }


  bool _syncing = false;
  bool get syncing => _syncing;
  set syncing(bool val) {
    _syncing = val;
    notifyListeners();
  }

  Future<void> syncAllEvents(BuildContext context) async {
    if (eventsToSync.isNotEmpty) {
      syncing = true;
      List<String> tempEventsToSync = List.from(_eventsToSync);
      for (String eventName in tempEventsToSync) {
        try {
          var data = await UserRepository.of(context).fetchDataForEvent(eventName);
          removeEventToSync(eventName);
          _handleEventAfterSync(eventName, data);
        } catch (e) {
          print('Error syncing event $eventName: $e');
        }
      }
      syncing = false;
    }
  }

  void _handleEventAfterSync(eventName, dynamic data) {

    switch(eventName) {
      case SocketEvents.registerUser:
        _handleRegisterUserEvent(data);
        break;
      case SocketEvents.startAuction:
        _handleStartAuctionEvent(data);
        break;
      case SocketEvents.getNewPlayer:
        _handleGetNewPlayerEvent(data);
        break;
      case SocketEvents.resetLastCallTimer:
        _handleResetLastCallTimerEvent(data);
        break;
      case SocketEvents.lastCall:
        _handleLastCallEvent(data);
        break;
      case SocketEvents.maxBidReached:
        _handleAuctionResetEvent(data);
        break;
      case SocketEvents.auctionCompleted:
        _handleAuctionResetEvent(data);
        break;
      case SocketEvents.minimumPlayersRequirement:
        _handleMinimumPlayersEvent(data);
        break;
      case SocketEvents.soldPlayer:
        _handlePlayerStatusEvent(data);
        break;
      case SocketEvents.Success:
        _handlePlayerStatusEvent(data);
        break;
      case SocketEvents.bidCompleted:
        _handlePlayerStatusEvent(data);
        break;
      case SocketEvents.playerUnsold:
        _handlePlayerStatusEvent(data);
        break;
      default:
        log('Unknown event: $eventName', name: 'SocketIO');
    }
  }

  AllUserResponse allUserResponse = AllUserResponse();

  Timer? timer;
  int timerDuration = 0;
  int elapsedSeconds = 0;
  int originalTimerDuration = 20;
  bool _isPaused = false;

  bool isMinimumReached = false;
  bool shouldShowButton = true;
  bool showGetAnotherPlayer = false;
  bool disableAllFields = false;
  bool isLastCallEnabledForPlayer = false;
  AuctionButtonState auctionButtonState = AuctionButtonState.start;

  final TextEditingController walletAmountController = TextEditingController();
  final TextEditingController timerController = TextEditingController();
  final TextEditingController batterC = TextEditingController();
  final TextEditingController wicketKeeperC = TextEditingController();
  final TextEditingController bowlerC = TextEditingController();
  final TextEditingController batterMaxC = TextEditingController();
  final TextEditingController wicketKeeperMaxC = TextEditingController();
  final TextEditingController bowlerMaxC = TextEditingController();
  final TextEditingController averagePlayerCostC = TextEditingController();

  @override
  void dispose() {
    _disposeSocket();
    _disposeTimer();
    _disposeControllers();
    super.dispose();
  }

  void _disposeSocket() {
    socket?.disconnect();
    socket?.dispose();
  }

  void _disposeTimer() {
    timer?.cancel();
  }

  void _disposeControllers() {
    dataStream.close();
    walletAmountController.dispose();
    timerController.dispose();
    batterC.dispose();
    wicketKeeperC.dispose();
    bowlerC.dispose();
    batterMaxC.dispose();
    wicketKeeperMaxC.dispose();
    bowlerMaxC.dispose();
    averagePlayerCostC.dispose();
  }

  void playerUnsoldScreenState(dynamic data) {
    try {
      disableAllFields = true;
      _isPaused = false;

      final response = PlayerStatusResponse.fromJson(data);
      if (response.text != null) {
        _updateFormFieldsFromModel(response.text!);
      }

      auctionButtonState = AuctionButtonState.next;
      notifyListeners();
    } catch (e) {
      log("Error in playerUnsoldScreenState: $e");
    }
  }

  void startLastCallTimer() {
    timer?.cancel();
    elapsedSeconds = 0;
    timer = Timer.periodic(const Duration(seconds: 1), _timerCallback);
  }

  void _timerCallback(Timer t) {
    if (_isPaused) {
      t.cancel();
      return;
    }

    if (elapsedSeconds < timerDuration) {
      elapsedSeconds++;
      notifyListeners();
    } else {
      t.cancel();
    }
  }

  void resetLastCallTimer() {
    timer?.cancel();
    elapsedSeconds = 0;
    notifyListeners();
  }

  void initSocketConnection({bool reconnect = false}) {
    try {
      if (socket == null || reconnect) {
        _cleanupExistingSocket(reconnect);
        _createNewSocketConnection();
        _setupSocketListeners();
        socket?.connect();

        log('Socket connection attempt initiated');
      }
    } catch (e, stackTrace) {
      log("establishing Socket.IO connection: $e", stackTrace: stackTrace);
    }
  }

  void _cleanupExistingSocket(bool reconnect) {
    if (socket != null && reconnect) {
      socket?.disconnect();
      socket = null;
    }
  }

  void _createNewSocketConnection() {
    log('Initializing Socket.IO connection to: ${ApiConstant.socketURL}');

    socket = IO.io(
        ApiConstant.socketURL,
        IO.OptionBuilder()
            .setTransports(['websocket'])
            .disableAutoConnect()
            .enableReconnection()
            .setReconnectionDelay(1000)
            .setReconnectionAttempts(5)
            .build());

    socket?.onConnect((_) {
      log('Socket.IO connection established');
      _registerUser();
    });

    socket?.onConnectError((data) {
      log('Socket.IO connection error: $data');
    });

    socket?.onDisconnect((_) {
      log('Socket.IO disconnected');
    });

    socket?.onError((data) {
      log('Socket.IO error: $data');
    });
  }

  void _registerUser() {
    log("Registering user with socket");
    final token = userRepository?.currentUser?.token ?? 'DefaultToken';
    socket?.emit(SocketEvents.registerUser,
        {'action': SocketEvents.registerUser, 'jwt': token});
    log("Sent registerUser event with token");
  }

  void _setupSocketListeners() {
    log("Setting up socket listeners");

    _setupBasicSocketListeners();
    _setupUserSocketListeners();
    _setupAuctionSocketListeners();
    _setupPlayerStatusSocketListeners();

    socket?.onAny((event, data) {
      log("Received $event event");
    });
  }

  void _setupBasicSocketListeners() {
    socket?.on(SocketEvents.registerUser, _handleRegisterUserEvent);
  }

  void _handleErrorEvent(dynamic data) {
    if(data['action']!=null && data['action'] != SocketEvents.error) {
      addEventToSync(data['action']);
    }
    log("Error event data: ${data.toString()}", name: 'SocketIO');
    log('Socket.IO Error: ${data['text']}',
        name: 'SocketIO', error: data['text']);
    showToast(data['text'] ?? "Something went wrong");
  }


  void _setupUserSocketListeners() {
    socket?.on(SocketEvents.getAllUserDetails, _handleGetAllUserDetailsEvent);
    socket?.on(SocketEvents.liveUsers, _handleLiveUsersEvent);
    socket?.on(SocketEvents.error, _handleErrorEvent);
  }

  void _setupAuctionSocketListeners() {
    socket?.on(SocketEvents.startAuction, _handleStartAuctionEvent);
    socket?.on(SocketEvents.resetLastCallTimer, _handleResetLastCallTimerEvent);
    socket?.on(SocketEvents.lastCall, _handleLastCallEvent);
    socket?.on(SocketEvents.getNewPlayer, _handleGetNewPlayerEvent);
    socket?.on(SocketEvents.maxBidReached, _handleAuctionResetEvent);
    socket?.on(SocketEvents.auctionCompleted, _handleAuctionResetEvent);
    socket?.on(
        SocketEvents.minimumPlayersRequirement, _handleMinimumPlayersEvent);
  }

  void _setupPlayerStatusSocketListeners() {
    socket?.on(SocketEvents.soldPlayer, _handlePlayerStatusEvent);
    socket?.on(SocketEvents.Success, _handlePlayerStatusEvent);
    socket?.on(SocketEvents.bidCompleted, _handlePlayerStatusEvent);
    socket?.on(SocketEvents.playerUnsold, _handlePlayerStatusEvent);
    socket?.on(SocketEvents.playerUnsold, _handlePlayerStatusEvent);
  }

  void _handleRegisterUserEvent(dynamic data) {
    try {
      log('registerUser event data: ${data.toString()}');

      final response = RegisterUserResponse.fromJson(data);
      if (response.ongoingAuctionFlag == true) {
        disableAllFields = true;
        notifyListeners();
      }
    } catch (e) {
      log("Error handling registerUser: $e");
    }
  }

  void _handleGetAllUserDetailsEvent(dynamic data) {
    log("Received Admin getAllUserDetails event with data: ${data.toString()}");

    try {
      if (data is Map<String, dynamic>) {
        allUserResponse = AllUserResponse.fromJson(data);

        if (allUserResponse.data?.users != null) {
          log("Found ${allUserResponse.data!.users!.length} users in the response");
        } else {
          log("No users found in the response");
        }

        notifyListeners();
      } else {
        log("Invalid data format received: $data");
      }
    } catch (e, stackTrace) {
      log('processing getAllUserDetails : $e', stackTrace: stackTrace);
    }
  }

  void _handleLiveUsersEvent(dynamic data) {
    log('liveUsers event data: ${data.toString()}');

    try {
      usersListing = BidTransaction.fromJson(data);
    } catch (e) {
      log("handling liveUsers: $e");
    }
  }

  void _handleStartAuctionEvent(dynamic data) {
    log('startAuction event data: ${data.toString()}');

    try {
      final response = PlayerStatusResponse.fromJson(data);

      if (response.text != null &&
          response.text.toString() != "User unauthorized") {
        updateAdminScreenState(data);
      }
      auctionButtonState = AuctionButtonState.ongoing;
      notifyListeners();
    } catch (e) {
      log("handling startAuction: $e");
    }
  }

  void _handleResetLastCallTimerEvent(dynamic data) {
    log('resetLastCallTimer event data: ${data.toString()}');

    try {
      isLastCallEnabledForPlayer = false;

      final response = PlayerStatusResponse.fromJson(data);

      if (response.text != null &&
          response.text.toString() != "User unauthorized" &&
          response.text?.remainingTime != null) {
        updateAdminScreenState(data);
      } else {
        timerDuration = originalTimerDuration;
        resetLastCallTimer();
      }
      dataStream.add(data);
      notifyListeners();
    } catch (e) {
      log("handling resetLastCallTimer: $e");
    }
  }

  void _handleLastCallEvent(dynamic data) {
    log('lastCall event data: ${data.toString()}');

    try {
      isLastCallEnabledForPlayer = true;
      timerDuration = originalTimerDuration;
      elapsedSeconds = 0;
      startLastCallTimer();

      auctionButtonState = AuctionButtonState.ongoing;
      notifyListeners();
    } catch (e) {
      log("handling lastCall: $e");
    }
  }

  void _handlePlayerStatusEvent(dynamic data) {
    final eventName = data is Map && data["action"] != null
        ? data["action"].toString()
        : "unknown event";
    log('Player status event ($eventName) data: ${data.toString()}');

    _handlePlayerStatusEvents(data);
  }

  void _handleGetNewPlayerEvent(dynamic data) {
    log('getNewPlayer event data: ${data.toString()}');

    try {
      isLastCallEnabledForPlayer = false;

      final response = PlayerStatusResponse.fromJson(data);

      if (response.totalPlayerLimitReached == true) {
        timerDuration = 0;
        auctionButtonState = AuctionButtonState.playerLimitReached;
        showToast(
            "The maximum number of players for the auction has been reached. Please restart the auction to add more players.");
      } else if (response.text != null &&
          response.text.toString() != "User unauthorized" &&
          response.text.toString() != 'User authorized') {
        updateAdminScreenState(data);
        auctionButtonState = AuctionButtonState.ongoing;
      }
      notifyListeners();
    } catch (e) {
      log("handling getNewPlayer: $e");
    }
  }

  void _handleAuctionResetEvent(dynamic data) {
    log('Auction reset event data: ${data.toString()}');

    _resetAuctionState(data);
  }

  void _handleMinimumPlayersEvent(dynamic data) {
    log('minimumPlayersRequirement event data: ${data.toString()}');

    try {
      final response = MinimumPlayersResponse.fromJson(data);

      if (response.data != null) {
        isMinimumReached = response.data?.isMinimumReached == true;
        notifyListeners();
      }
    } catch (e) {
      log("handling minimumPlayersRequirement: $e");
    }
  }

  void _handlePlayerStatusEvents(dynamic data) {
    try {
      isLastCallEnabledForPlayer = false;

      final response = PlayerStatusResponse.fromJson(data);
      String action = response.action ?? "";

      if (response.text != null &&
          response.text.toString() != "User unauthorized" &&
          response.text?.remainingTime != null &&
          action != SocketEvents.playerUnsold) {
        updateAdminScreenState(data);
      } else {
        showGetAnotherPlayer = true;
        auctionButtonState = AuctionButtonState.next;

        if (action == SocketEvents.playerUnsold) {
          playerUnsoldScreenState(data);
        }

        if (action == SocketEvents.bidCompleted ||
            action == SocketEvents.soldPlayer) {
          if (response.text != null && response.text?.amount != null) {
            walletAmountController.text = response.text!.amount.toString();
          }
          auctionButtonState = AuctionButtonState.next;
        }
      }

      notifyListeners();
    } catch (e) {
      log("_handlePlayerStatusEvents: $e");
    }
  }

  void _resetAuctionState(dynamic data) {
    try {
      isLastCallEnabledForPlayer = false;
      disableAllFields = false;
      showGetAnotherPlayer = false;
      auctionButtonState = AuctionButtonState.start;

      _clearAllFormFields();

      dataStream.add(data);
      notifyListeners();
    } catch (e) {
      log("_resetAuctionState : $e");
    }
  }

  void _clearAllFormFields() {
    walletAmountController.clear();
    timerController.clear();
    batterC.clear();
    wicketKeeperC.clear();
    bowlerC.clear();
    batterMaxC.clear();
    wicketKeeperMaxC.clear();
    bowlerMaxC.clear();
    averagePlayerCostC.clear();
  }

  void updateAdminScreenState(dynamic data) {
    try {
      disableAllFields = true;
      isLastCallEnabledForPlayer = false;

      final response = PlayerStatusResponse.fromJson(data);

      if (response.text != null) {
        _isPaused = response.text?.pauseActivated == true;
        _updateFormFieldsFromModel(response.text!);
      }

      String action = response.action ?? "";
      auctionButtonState =
          AuctionButtonStateHelper.determineButtonState(action, _isPaused);

      notifyListeners();
    } catch (e, stackTrace) {
      log("updateAdminScreenState: $e", stackTrace: stackTrace);
    }
  }

  void _updateFormFieldsFromModel(PlayerData playerData) {
    if (playerData.amount != null) {
      walletAmountController.text = playerData.amount.toString();
    }

    batterC.text = (playerData.batter ?? "").toString();
    wicketKeeperC.text = (playerData.wicketKeeper ?? "").toString();
    bowlerC.text = (playerData.bowler ?? "").toString();
    batterMaxC.text = (playerData.batterMax ?? "").toString();
    wicketKeeperMaxC.text = (playerData.wicketKeeperMax ?? "").toString();
    bowlerMaxC.text = (playerData.bowlerMax ?? "").toString();
    averagePlayerCostC.text = (playerData.averagePlayerCost ?? "").toString();
  }

  void wsStartAuction() {
    try {
      if (!_ensureSocketConnected(() => wsStartAuction())) {
        return;
      }

      _validateAndEmitStartAuction();

      shouldShowButton = false;
      notifyListeners();
    } catch (e) {
      log("wsStartAuction: $e");
      showToast(e.toString());
      rethrow;
    }
  }

  bool _ensureSocketConnected(Function retryCallback) {
    if (socket == null || socket?.connected != true) {
      log("Socket not connected. Attempting to reconnect...");
      initSocketConnection(reconnect: true);
      Future.delayed(const Duration(seconds: 1), () => retryCallback());
      return false;
    }
    return true;
  }

  void _validateAndEmitStartAuction() {
    Map<String, int?> values = _parseFormValues();
    _validateAuctionParams(values);

    disableAllFields = true;

    final data = _createStartAuctionData(values);

    log("Emitting startAuction with data: $data");
    socket?.emit(SocketEvents.startAuction, data);
  }

  Map<String, int?> _parseFormValues() {
    Map<String, int?> values = {};

    try {
      values['walletAmount'] = int.parse(walletAmountController.text);
      values['batter'] = int.parse(batterC.text);
      values['wicketKeeper'] = int.parse(wicketKeeperC.text);
      values['bowler'] = int.parse(bowlerC.text);
      values['batterMax'] = int.parse(batterMaxC.text);
      values['wicketKeeperMax'] = int.parse(wicketKeeperMaxC.text);
      values['bowlerMax'] = int.parse(bowlerMaxC.text);
      values['averagePlayerCost'] = int.parse(averagePlayerCostC.text);
    } catch (e) {
      log("parsing input values: $e");
    }

    return values;
  }

  void _validateAuctionParams(Map<String, int?> values) {
    if (timerDuration <= 0) {
      throw Exception('Please enter a valid timer duration.');
    }

    Map<String, String> validationMap = {
      'walletAmount': 'Please enter a valid wallet amount.',
      'batter': 'Please enter a valid batsman count.',
      'wicketKeeper': 'Please enter a valid wicket keeper count.',
      'bowler': 'Please enter a valid bowler count.',
      'batterMax': 'Please enter a valid max batsman count.',
      'wicketKeeperMax': 'Please enter a valid max wicket keeper count.',
      'bowlerMax': 'Please enter a valid max bowler count.',
      'averagePlayerCost': 'Please enter a valid average player cost.'
    };

    for (var entry in validationMap.entries) {
      if (values[entry.key] == null || values[entry.key]! <= 0) {
        throw Exception(entry.value);
      }
    }
  }

  Map<String, dynamic> _createStartAuctionData(Map<String, int?> values) {
    return {
      "action": SocketEvents.startAuction,
      "amount": values['walletAmount'],
      "batter": values['batter'],
      "wicketKeeper": values['wicketKeeper'],
      "bowler": values['bowler'],
      "batterMax": values['batterMax'],
      "wicketKeeperMax": values['wicketKeeperMax'],
      "bowlerMax": values['bowlerMax'],
      "duration": timerDuration,
      "averagePlayerCost": values['averagePlayerCost'],
      'jwt': userRepository?.currentUser?.token ?? 'DefaultToken'
    };
  }

  void wsGetAnotherPlayer() {
    try {
      if (!_ensureSocketConnected(() => wsGetAnotherPlayer())) {
        return;
      }

      log("Requesting new player");
      socket?.emit(SocketEvents.getNewPlayer,
          _createTokenPayload(action: SocketEvents.getNewPlayer));

      resetLastCallTimer();
      showGetAnotherPlayer = !showGetAnotherPlayer;
      auctionButtonState = AuctionButtonState.pause;
      notifyListeners();
    } catch (e) {
      log("wsGetAnotherPlayer: $e");
    }
  }

  void wsStopAuction() {
    try {
      if (!_ensureSocketConnected(() => wsStopAuction())) {
        return;
      }

      log("Stopping auction");
      socket?.emit(SocketEvents.auctionCompleted,
          _createTokenPayload(action: SocketEvents.auctionCompleted));
    } catch (e) {
      log("wsStopAuction: $e");
    }
  }

  void wsLastCallOption() {
    try {
      if (!_ensureSocketConnected(() => wsLastCallOption())) {
        return;
      }

      log("Emitting last call");
      Map<String, dynamic> data =
          _createTokenPayload(action: SocketEvents.lastCall);
      data["duration"] = 20;
      socket?.emit(SocketEvents.lastCall, data);
    } catch (e) {
      log("wsLastCallOption: $e");
    }
  }

  Map<String, dynamic> _createTokenPayload({required String action}) {
    final token = userRepository?.currentUser?.token;

    return {
      'action': action,
      'jwt': token ?? 'DefaultToken',
    };
  }

  void getAllUserDetails(String token) {
    try {
      log('Requesting all user details', name: 'SocketIO');

      if (socket == null || !socket!.connected) {
        log('Socket not connected, attempting to connect first',
            name: 'SocketIO');
        initSocketConnection();
        _scheduleGetAllUserDetails(token);
      } else {
        _sendGetAllUserDetailsRequest(token);
      }
    } catch (e, stackTrace) {
      log('getting all user details : $e',
          stackTrace: stackTrace, name: 'SocketIO');
    }
  }

  void _scheduleGetAllUserDetails(String token) {
    Future.delayed(const Duration(seconds: 1), () {
      if (socket?.connected == true) {
        _sendGetAllUserDetailsRequest(token);
      } else {
        log('Socket still not connected after delay, retrying getAllUserDetails',
            name: 'SocketIO');
        Future.delayed(const Duration(seconds: 2), () {
          _sendGetAllUserDetailsRequest(token);
        });
      }
    });
  }

  void _sendGetAllUserDetailsRequest(String token) {
    socket?.emit(SocketEvents.getAllUserDetails, {
      'action': SocketEvents.getAllUserDetails,
      'jwt': token,
    });
    log('All user details request sent successfully');
  }
}
