import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boilerplate/app_manager/api/api_call.dart';
import 'package:flutter_boilerplate/app_manager/api/project_response.dart';
import 'package:flutter_boilerplate/app_manager/component/alert_dialog/alert_dialog_view.dart';
import 'package:flutter_boilerplate/app_manager/component/bottom_sheet/custom_bottom_sheet.dart';
import 'package:flutter_boilerplate/app_manager/component/bottom_sheet/functional_sheet.dart';
import 'package:flutter_boilerplate/app_manager/constant/storage_constant.dart';
import 'package:flutter_boilerplate/app_manager/helper/local_storage.dart';
import 'package:flutter_boilerplate/authentication/user.dart';
import 'package:flutter_boilerplate/util/logger.dart';
import 'package:flutter_boilerplate/view/screens/screens.dart';
import 'package:flutter_boilerplate/view_model/web_socket_view_model.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;

import '../app_manager/helper/show_toast.dart';

class UserRepository extends ChangeNotifier {
  http.Client client = http.Client();
  User? currentUser;
  bool? userLoading;

  UserRepository({
    this.currentUser,
  });

  bool get isUserLoading => userLoading ?? false;

  User get getUser => currentUser!;

  bool get isLoggedIn => currentUser?.id != null;

  static UserRepository of(BuildContext context) =>
      Provider.of<UserRepository>(context, listen: false);

  final ApiCall _apiCall = ApiCall();

  Future updateUserData(User? userData) async {
    try {
      if (userData == null) {
        await LocalStorage.remove(key: StorageConstant.userStorage);
        currentUser = null;
      } else {
        String user = jsonEncode(userData.toJson());
        await LocalStorage.setString(
            key: StorageConstant.userStorage, data: user, useEncrypt: true);
        currentUser = await fetchLocalUserData();
        log("current User is ${currentUser!.toJson()}");
      }
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print(e.toString());
      }
    }
  }

  static Future<User?> fetchLocalUserData() async {
    try {
      String? storedData = await LocalStorage.getString(
          key: StorageConstant.userStorage, useEncrypt: true);
      log("stored data is $storedData");
      if (storedData != null) {
        User user = User.fromJson(jsonDecode(storedData));
        await LocalStorage.setString(
            key: StorageConstant.userToken, data: user.token ?? "");
        return user;
      }
      return null;
    } catch (e) {
      log("error 3 $e");
    }
    return null;
  }

  void changePassword(BuildContext context) {}

  Future signOutUser(BuildContext context, {VoidCallback? logedOut}) async {
    final webSocketViewModel =
        Provider.of<WebSocketViewModel>(context, listen: false);

    if (kIsWeb) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialogView(
              key: const Key("sign_out"),
              title: 'flutter_boilerplate'.tr(),
              message: 'sign-out-message'.tr(),
              popButtonTitle: 'cancel'.tr(),
              successButtonTitle: 'sign_out'.tr(),
              onPressFunction: () async {
                if (logedOut != null) {
                  logedOut();
                }
                directLogOut(context, webSocketViewModel);
              });
        },
      );
    } else {
      CustomBottomSheet.open(context,
          child: FunctionalSheet(
              key: const Key("sign_out"),
              message: "sign-out-message".tr(),
              buttonName: "sign_out".tr(),
              onPressButton: () async {
                if (logedOut != null) {
                  logedOut();
                }
                directLogOut(context, webSocketViewModel);
              }));
    }
  }

  Future directLogOut(
      BuildContext context, WebSocketViewModel webSocketViewModel) async {
    try {
      log("Disconnecting WebSocket before logout");
      await webSocketViewModel.disconnectClient();

      log("Clearing user data");
      await updateUserData(null);

      log("Navigating to sign in screen");
      while (context.canPop()) {
        context.pop();
      }
      Router.neglect(context, () => context.goNamed(SignInScreen.name));
    } catch (e) {
      log("Error during logout: $e");
      rethrow;
    }
  }

  Future updateToken(String token) async {
    User newUserData = getUser;
    newUserData.token = token;
    updateUserData(newUserData);
  }

  Future<void> fetchUser() async {
    userLoading = true;
    try {
      String? token = await LocalStorage.getString(
        key: StorageConstant.userToken,
      );
      log("current user is $currentUser");
      var response = await _apiCall.call(
          url: "user/details",
          client: client,
          apiCallType: ApiCallType.get(),
          useThisToken: currentUser != null ? currentUser!.token : token,
          token: true);
      log("reponse is $response");
      ProjectResponse projectResponse = ProjectResponse.fromJson(response);
      if (projectResponse.status == 1) {
        currentUser = User.fromJson(projectResponse.data);
      } else {
        showToast(
          projectResponse.message ?? "",
        );
      }
      userLoading = false;
      notifyListeners();
    } catch (e) {
      userLoading = false;
      notifyListeners();
      log("error2 is $e");
    }
  }

  void updateUserBalances({int? remainingBalance, int? bonusBalance}) {
    if (currentUser == null) {
      log("UserRepository: Can't update balances - currentUser is null",
          name: 'UserRepository');
      return;
    }

    bool changed = false;

    if (remainingBalance != null &&
        currentUser!.remainingBalance != remainingBalance) {
      currentUser!.remainingBalance = remainingBalance;
      changed = true;
    }

    if (bonusBalance != null && currentUser!.userBonusBalance != bonusBalance) {
      currentUser!.userBonusBalance = bonusBalance;
      changed = true;
    }

    if (changed) {
      log("UserRepository: Balances updated - remaining: ${currentUser!.remainingBalance}, bonus: ${currentUser!.userBonusBalance}",
          name: 'UserRepository');
      notifyListeners();
    }
  }

  void updateUserFromSocketData(Map<String, dynamic> userData) {
    if (userData.isEmpty) {
      log("UserRepository: Empty user data received", name: 'UserRepository');
      return;
    }

    try {

      currentUser!.remainingBalance = userData['remaining_balance'] ?? 0;
      currentUser!.userBonusBalance = userData['bonus_balance'] ?? 0;
      currentUser!.walletAmount = userData['wallet_amount'];

      if (userData['player_transactions'] != null) {
        currentUser!.playerTransactions =
            _parsePlayerTransactions(userData['player_transactions']);
      }

      log("UserRepository: User updated from socket data - ID: ${currentUser!.id}, Transactions: ${currentUser!.playerTransactions?.length ?? 0}",
          name: 'UserRepository');

      notifyListeners();
    } catch (e, stackTrace) {
      log("UserRepository: Error updating user from socket data: $e",
          name: 'UserRepository', error: e, stackTrace: stackTrace);
    }
  }

  List<PlayerTransaction> _parsePlayerTransactions(List<dynamic> transactions) {
    return transactions.map((transaction) {
      return PlayerTransaction.fromJson(transaction);
    }).toList();
  }
}
