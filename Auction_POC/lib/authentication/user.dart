import 'package:flutter_boilerplate/models/bid_transaction.dart';


class User {
  String id;
  String name;
  String phone;
  int remainingBalance;
  int? userBonusBalance;
  int? walletAmount;
  int role;
  DateTime createdAt;
  DateTime updatedAt;
  List<PlayerTransaction> playerTransactions;
  String? token;
  PurchaseSummary? purchaseSummary;

  User({
    required this.id,
    required this.name,
    required this.phone,
    required this.remainingBalance,
    this.userBonusBalance,
    this.walletAmount,
    required this.role,
    required this.createdAt,
    required this.updatedAt,
    required this.playerTransactions,
    this.token,
    this.purchaseSummary,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json["id"],
      name: json["name"],
      phone: json["phone"],
      remainingBalance: json["remaining_balance"],
      userBonusBalance:
          (json["bonus_balance"] ?? 0) < 0 ? 0 : json["bonus_balance"],
      walletAmount: json["wallet_amount"],
      role: json["role"],
      createdAt: DateTime.parse(json["createdAt"]),
      updatedAt: DateTime.parse(json["updatedAt"]),
      playerTransactions: json["player_transactions"] != null
          ? List<PlayerTransaction>.from(json["player_transactions"]
              .map((x) => PlayerTransaction.fromJson(x)))
          : [],
      token: json["token"],
      purchaseSummary: json["purchaseSummary"] != null
          ? PurchaseSummary.fromJson(json["purchaseSummary"])
          : null,
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "phone": phone,
        "remaining_balance": remainingBalance,
        "bonus_balance": userBonusBalance,
        "wallet_amount": walletAmount,
        "role": role,
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
        "player_transactions":
            List<dynamic>.from(playerTransactions.map((x) => x.toJson())),
        "token": token,
        "purchaseSummary": purchaseSummary?.toJson(),
      };
}

class PlayerTransaction {
  String id;
  String playerId;
  String buyerId;
  DateTime createdAt;
  DateTime updatedAt;
  Player player;

  PlayerTransaction({
    required this.id,
    required this.playerId,
    required this.buyerId,
    required this.createdAt,
    required this.updatedAt,
    required this.player,
  });

  factory PlayerTransaction.fromJson(Map<String, dynamic> json) =>
      PlayerTransaction(
        id: json["id"],
        playerId: json["player_id"],
        buyerId: json["buyer_id"],
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
        player: Player.fromJson(json["player"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "player_id": playerId,
        "buyer_id": buyerId,
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
        "player": player.toJson(),
      };
}

class Player {
  String id;
  String name;
  int teamId;
  int typeId;
  int basePrice;
  int buyPrice;
  String status;
  DateTime? createdAt;
  DateTime? updatedAt;
  PlayerType? playerType;

  Player({
    required this.id,
    required this.name,
    required this.teamId,
    required this.typeId,
    required this.basePrice,
    required this.buyPrice,
    required this.status,
    this.createdAt,
    this.updatedAt,
    required this.playerType,
  });

  factory Player.fromJson(Map<String, dynamic> json) => Player(
        id: json["id"],
        name: json["name"],
        teamId: json["team_id"],
        typeId: json["type_id"],
        basePrice: json["base_price"],
        buyPrice: json["buy_price"] ?? 0,
        status: json["status"],
        createdAt: json["createdAt"] != null
            ? DateTime.tryParse(json["createdAt"])
            : null,
        updatedAt: json["updatedAt"] != null
            ? DateTime.tryParse(json["updatedAt"])
            : null,
        playerType: json['player_type'] != null
            ? PlayerType.fromJson(json['player_type'])
            : null,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "team_id": teamId,
        "type_id": typeId,
        "base_price": basePrice,
        "buy_price": buyPrice,
        "status": status,
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
        "player_type": playerType!.toJson(),
      };
}

class PurchaseSummary {
  int totalPurchased;
  int bowlers;
  int batsmen;
  int wicketKeepers;

  PurchaseSummary({
    required this.totalPurchased,
    required this.bowlers,
    required this.batsmen,
    required this.wicketKeepers,
  });

  factory PurchaseSummary.fromJson(Map<String, dynamic> json) =>
      PurchaseSummary(
        totalPurchased: json["totalPurchased"],
        bowlers: json["bowlers"],
        batsmen: json["batsmen"],
        wicketKeepers: json["wicketKeepers"],
      );

  Map<String, dynamic> toJson() => {
        "totalPurchased": totalPurchased,
        "bowlers": bowlers,
        "batsmen": batsmen,
        "wicketKeepers": wicketKeepers,
      };
}
