class BidTransaction {
  String? socketId;
  bool? status;
  String? action;
  TextData? text;
  Data? data;
  int? remainingTime;

  BidTransaction(
      {this.socketId,
      this.status,
      this.action,
      this.text,
      this.data,
      this.remainingTime});

  BidTransaction.fromJson(Map<String, dynamic> json) {
    remainingTime = json['remainingTime'];
    socketId = json['socketId'];
    status = json['status'];
    action = json['action'];
    if (json['text'] != null) {
      if (json['text'] is String) {
        text = TextData(text: json['text']);
      } else {
        text = TextData.fromJson(json['text']);
      }
    }
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['remainingTime'] = remainingTime;
    data['socketId'] = socketId;
    data['status'] = status;
    data['action'] = action;
    if (text != null) {
      data['text'] = text!.toJson();
    }
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class TextData {
  int? higghestBid;
  String? text;
  String? higghestBidder;
  String? higghestBidderId;
  Player? player;
  String? bidId;
  int? timerDuration;
  int? bowlerMax;
  int? wicketKeeperMax;
  int? batterMax;
  bool? biddable;
  bool? isBelowAverage;
  bool? pauseActivated;

  TextData({
    this.higghestBid,
    this.text,
    this.higghestBidder,
    this.higghestBidderId,
    this.player,
    this.bidId,
    this.timerDuration,
    this.bowlerMax,
    this.wicketKeeperMax,
    this.batterMax,
    this.biddable,
    this.isBelowAverage,
    this.pauseActivated,
  });

  TextData.fromJson(Map<String, dynamic> json) {
    higghestBid = json['higghestBid'];
    text = json['text'];
    higghestBidder = json['higghestBidder'];
    higghestBidderId = json['higghestBidderId'];
    player = json['player'] != null
        ? ((json['player'] is String)
            ? Player(name: json['player'])
            : Player.fromJson(json['player']))
        : null;
    bidId = json['bidId'];
    timerDuration = json['timerDuration'];
    bowlerMax = json['bowlerMax'];
    wicketKeeperMax = json['wicketKeeperMax'];
    batterMax = json['batterMax'];
    isBelowAverage = json['isBelowAverage'] ?? false;
    biddable = json['biddable'] ?? true;
    pauseActivated = json['pauseActivated'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['higghestBid'] = higghestBid;
    data['text'] = text;
    data['higghestBidder'] = higghestBidder;
    data['higghestBidderId'] = higghestBidderId;
    if (player != null) {
      data['player'] = player!.toJson();
    }
    data['bidId'] = bidId;
    data['timerDuration'] = timerDuration;
    data['bowlerMax'] = bowlerMax;
    data['wicketKeeperMax'] = wicketKeeperMax;
    data['batterMax'] = batterMax;
    data['biddable'] = biddable;
    data['isBelowAverage'] = isBelowAverage;
    data['pauseActivated'] = pauseActivated;
    return data;
  }
}

class Player {
  String? id;
  String? name;
  int? teamId;
  int? typeId;
  int? basePrice;
  int? buyPrice;
  String? status;
  String? createdAt;
  String? updatedAt;
  Team? team;
  PlayerType? playerType;

  Player(
      {this.id,
      this.name,
      this.teamId,
      this.typeId,
      this.basePrice,
      this.buyPrice,
      this.status,
      this.createdAt,
      this.updatedAt,
      this.team,
      this.playerType});

  Player.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    teamId = json['team_id'];
    typeId = json['type_id'];
    basePrice = json['base_price'];
    buyPrice = json['buy_price'];
    status = json['status'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    team = json['team'] != null ? Team.fromJson(json['team']) : null;
    playerType = json['player_type'] != null
        ? PlayerType.fromJson(json['player_type'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['team_id'] = teamId;
    data['type_id'] = typeId;
    data['base_price'] = basePrice;
    data['buy_price'] = buyPrice;
    data['status'] = status;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    if (team != null) {
      data['team'] = team!.toJson();
    }
    if (playerType != null) {
      data['player_type'] = playerType!.toJson();
    }
    return data;
  }
}

class Team {
  int? id;
  String? name;
  String? createdAt;
  String? updatedAt;

  Team({this.id, this.name, this.createdAt, this.updatedAt});

  Team.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    return data;
  }
}

class PlayerType {
  int? id;
  String? type;
  String? createdAt;
  String? updatedAt;

  PlayerType({this.id, this.type, this.createdAt, this.updatedAt});

  PlayerType.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    type = json['type'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['type'] = type;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    return data;
  }
}

class Data {
  List<Users>? users;

  Data({this.users});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['users'] != null) {
      users = <Users>[];
      json['users'].forEach((v) {
        users!.add(Users.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (users != null) {
      data['users'] = users!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Users {
  String? id;
  String? name;
  int? remainingBalance;

  Users({this.id, this.name, this.remainingBalance});

  Users.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    remainingBalance = json['remaining_balance'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['remaining_balance'] = remainingBalance;
    return data;
  }
}
