import 'package:flutter_boilerplate/app_manager/constant/socket_events.dart';
import 'package:flutter_boilerplate/view_model/admin_dashboard_view_model.dart';

class PlayerStatusResponse {
  final String? action;
  final PlayerData? text;
  final bool? totalPlayerLimitReached;

  PlayerStatusResponse({
    this.action,
    this.text,
    this.totalPlayerLimitReached,
  });

  factory PlayerStatusResponse.fromJson(Map<String, dynamic> json) {
    return PlayerStatusResponse(
      action: json['action'] as String?,
      text: json['text'] is Map<String, dynamic>
          ? PlayerData.fromJson(json['text'] as Map<String, dynamic>)
          : null,
      totalPlayerLimitReached: json['totalPlayerLimitReached'] as bool?,
    );
  }
}

class PlayerData {
  final int? amount;
  final int? batter;
  final int? wicketKeeper;
  final int? bowler;
  final int? batterMax;
  final int? wicketKeeperMax;
  final int? bowlerMax;
  final int? averagePlayerCost;
  final int? remainingTime;
  final bool? pauseActivated;

  PlayerData({
    this.amount,
    this.batter,
    this.wicketKeeper,
    this.bowler,
    this.batterMax,
    this.wicketKeeperMax,
    this.bowlerMax,
    this.averagePlayerCost,
    this.remainingTime,
    this.pauseActivated,
  });

  factory PlayerData.fromJson(Map<String, dynamic> json) {
    return PlayerData(
      amount: json['amount'] is int
          ? json['amount'] as int
          : int.tryParse(json['amount']?.toString() ?? ''),
      batter: json['batter'] is int
          ? json['batter'] as int
          : int.tryParse(json['batter']?.toString() ?? ''),
      wicketKeeper: json['wicketKeeper'] is int
          ? json['wicketKeeper'] as int
          : int.tryParse(json['wicketKeeper']?.toString() ?? ''),
      bowler: json['bowler'] is int
          ? json['bowler'] as int
          : int.tryParse(json['bowler']?.toString() ?? ''),
      batterMax: json['batterMax'] is int
          ? json['batterMax'] as int
          : int.tryParse(json['batterMax']?.toString() ?? ''),
      wicketKeeperMax: json['wicketKeeperMax'] is int
          ? json['wicketKeeperMax'] as int
          : int.tryParse(json['wicketKeeperMax']?.toString() ?? ''),
      bowlerMax: json['bowlerMax'] is int
          ? json['bowlerMax'] as int
          : int.tryParse(json['bowlerMax']?.toString() ?? ''),
      averagePlayerCost: json['averagePlayerCost'] is int
          ? json['averagePlayerCost'] as int
          : int.tryParse(json['averagePlayerCost']?.toString() ?? ''),
      remainingTime: json['remainingTime'] is int
          ? json['remainingTime'] as int
          : int.tryParse(json['remainingTime']?.toString() ?? ''),
      pauseActivated: json['pauseActivated'] as bool?,
    );
  }
}

class MinimumPlayersResponse {
  final String? action;
  final MinimumPlayersData? data;

  MinimumPlayersResponse({
    this.action,
    this.data,
  });

  factory MinimumPlayersResponse.fromJson(Map<String, dynamic> json) {
    return MinimumPlayersResponse(
      action: json['action'] as String?,
      data: json['data'] is Map<String, dynamic>
          ? MinimumPlayersData.fromJson(json['data'] as Map<String, dynamic>)
          : null,
    );
  }
}

class MinimumPlayersData {
  final bool? isMinimumReached;

  MinimumPlayersData({
    this.isMinimumReached,
  });

  factory MinimumPlayersData.fromJson(Map<String, dynamic> json) {
    return MinimumPlayersData(
      isMinimumReached: json['isMinimumReached'] as bool?,
    );
  }
}

class RegisterUserResponse {
  final String? action;
  final bool? ongoingAuctionFlag;

  RegisterUserResponse({
    this.action,
    this.ongoingAuctionFlag,
  });

  factory RegisterUserResponse.fromJson(Map<String, dynamic> json) {
    return RegisterUserResponse(
      action: json['action'] as String?,
      ongoingAuctionFlag: json['ongoingAuctionFlag'] as bool?,
    );
  }
}

class AuctionButtonStateHelper {
  static AuctionButtonState determineButtonState(String action, bool isPaused) {
    if (action == SocketEvents.bidCompleted) {
      return AuctionButtonState.next;
    } else if (isPaused) {
      return AuctionButtonState.resume;
    } else {
      return AuctionButtonState.pause;
    }
  }
}
