import 'dart:convert';

Auction auctionFromJson(String str) => Auction.fromJson(json.decode(str));

String auctionToJson(Auction data) => json.encode(data.toJson());

class Auction {
  String playerName;
  String playerId;
  String playerType;
  int basePrice;
  String leadingBidder;
  int leadingBid;
  String leadingBidderId;

  Auction(
      {required this.playerName,
      required this.playerId,
      required this.basePrice,
      required this.leadingBidder,
      required this.leadingBid,
      required this.leadingBidderId,
      required this.playerType});

  factory Auction.fromJson(Map<String, dynamic> json) => Auction(
      playerName: json["playerName"],
      playerId: json["playerId"],
      basePrice: json["basePrice"],
      leadingBidder: json["leadingBidder"],
      leadingBid: json["leadingBid"],
      leadingBidderId: json["leadingBidderId"],
      playerType: json["playerType"]);

  Map<String, dynamic> toJson() => {
        "playerName": playerName,
        "basePrice": basePrice,
        "leadingBidder": leadingBidder,
        "leadingBid": leadingBid,
        "playerId": playerId,
        "leadingBidderId": leadingBidderId,
        "playerType": playerType
      };
}
