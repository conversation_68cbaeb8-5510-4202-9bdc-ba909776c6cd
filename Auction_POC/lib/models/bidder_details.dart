// To parse this JSON data, do
//
//     final bidderDetails = bidderDetailsFromJson(jsonString);

import 'dart:convert';

BidderDetails bidderDetailsFromJson(String str) => BidderDetails.fromJson(json.decode(str));

String bidderDetailsToJson(BidderDetails data) => json.encode(data.toJson());

class BidderDetails {
    int higghestBid;
    String higghestBidder;
    String player;
    String bidId;
  String higghestBidderId;

    BidderDetails({
        required this.higghestBid,
        required this.higghestBidder,
        required this.player,
        required this.bidId,
      required this.higghestBidderId
    });

    factory BidderDetails.fromJson(Map<String, dynamic> json) => BidderDetails(
        higghestBid: json["higghestBid"],
        higghestBidder: json["higghestBidder"],
        player: json["player"],
        bidId: json["bidId"],
        higghestBidderId: json["higghestBidderId"],
    );

    Map<String, dynamic> toJson() => {
        "higghestBid": higghestBid,
        "higghestBidder": higghestBidder,
        "player": player,
        "bidId": bidId,
        "higghestBidderId": higghestBidderId
    };
}
