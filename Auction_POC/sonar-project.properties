# must be unique in a given SonarQube instance
#----- Default SonarQube server
sonar.login=****************************************
sonar.host.url=https://quality.growexx.com/

sonar.jdbc.username=sonar
sonar.jdbc.password=sonar


sonar.projectKey=auction_poc
sonar.projectName=auction_poc
sonar.projectVersion=1.0
sonar.language=dart
# Path is relative to the sonar-project.properties file. Replace "\" by "/" on Windows.
# Since SonarQube 4.2, this property is optional if sonar.modules is set.
# If not set, SonarQube starts looking for source code from the directory containing
# the sonar-project.properties file.
sonar.sources=./lib
sonar.tests=./test
sonar.sourceEncoding=UTF-8
sonar.javascript.lcov.reportPaths=./coverage/lcov.info
sonar.exclusions=test/**/*_test.mocks.dart,lib/**/*.g.dart
#----- Default source code encoding

 