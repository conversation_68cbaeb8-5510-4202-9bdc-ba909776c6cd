{"app_name": "Auction", "login_screen": "Login Screen", "dashboard_screen": "Dashboard", "go_to_dashboard": "Go To Dashboard", "login_using_social_networks": "Login using Social Networks", "or_capital": "OR", "email": "Email", "password": "Password", "sign_in": "<PERSON><PERSON>", "enter_name": "Enter Name", "enter_password": "Enter Password", "enter_mobile": "Enter Mobile Number", "password_hint": "8 characters minimum\nOne uppercase character\nOne lowercase character\nOne special character( !, @, #, \\$, %, ^, &, *, _ )", "remember_me": "Remember Me", "forgot_password": "Forgot Password?", "already_have_an_account": "Already have an account?", "sign_up": "Sign Up", "otp_get_started": "Let's get started", "otp_description": "Never a better time than no to start.", "send_otp": "Send OTP", "add_your_phone_number": "Add your phone number. we'll send you a verification code so we know you're real", "send": "Send", "verification": "Verification", "enter_otp": "Enter your OTP code number", "verify": "Verify", "did_not_receive_code": "Didn't you receive any code?", "resend_new_code": "Resend OTP", "change_password": "Change Password", "forgot_password_description": "Please enter the email address you'd like your password reset information sent to", "request_reset_link": "Request reset link", "back_to_login": "Back To Login", "pay_now": "Pay Now", "flutter_description1": "Flutter is Google's portable UI toolkit for crafting beautiful, natively compiled applications for mobile, web, and desktop from a single codebase. Flutter works with existing code, is used by developers and organizations around the world, and is free and open source.", "flutter_description2": "For users, Flutter makes beautiful apps come to life. For developers, Flutter lowers the bar to entry for building apps. It speeds app development and reduces the cost and complexity of app production across platforms. For designers, Flutter provides a canvas for high-end user experiences. Fast Company described Flutter as one of the top design ideas of the decade for its ability to turn concepts into production code without the compromises imposed by typical frameworks. It also acts as a productive prototyping tool, with CodePen support for sharing your ideas with others. For engineering managers and businesses, Flutter allows the unification of app developers into a single mobile, web, and desktop app team, building branded apps for multiple platforms out of a single codebase. Flutter speeds feature development and synchronizes release schedules across the entire customer base.", "welcome": "Welcome ", "sign_out": "Sign Out", "create_security_pin": "Create Security PIN", "security_pin": "Security PIN", "verify_security_pin": "Verify Security PIN", "security_pin_description": "Set Your Security PIN", "your_4_digit_pin": "Your 4 digit PIN", "enter_security_pin": "Enter Your Security PIN", "save": "Save", "okay": "Okay", "security_pin_description_done": "Security PIN created successfully", "payment_screen": "Payment Screen", "payment": "Payment", "editprofile_screen": "Edit Profile Screen", "editprofile": "Edit Profile", "enter_first_name": "Enter First Name", "enter_last_name": "Enter Last Name", "confirm_password": "Confirm Password", "confirm_new_password": "Confirm New Password", "old_password": "Old Password", "new_password": "New Password", "mobile_number": "Mobile Number", "mandatory_field": "This is a mandatory field.", "error_valid_name": "Please enter valid name.", "error_password_policy": "The entered password does not match the password policy.", "error_confirm_password": "The passwords you have entered do not match. Please try again.", "error_valid_email_address": "Please enter a valid email address.", "error_valid_mobile_number": "Please enter a valid mobile number.", "sign_in_with_otp": "Sign In with OTP", "verify_pin": "Verify PIN", "want_to_exit": "Are you sure? You want to exit?", "exit": "exit", "location_services_are_disabled": "Location services are disabled. Please enable the services", "location_permission_denied": "Location permissions are denied", "location_permission_permanently_denied": "Location permissions are permanently denied, we cannot request permissions.", "google-map-functionality": "Google Map Functionality\n(WEB)", "your_location": "\nYour Location: ", "fetching_location": "Fetching Current Location...", "browse_gallery": "Browse Gallery", "use_a_camera": "Use a Camera", "pick_image": "Pick\nImage", "todo_screen": "Todo Screen", "todo_list": "Todo List", "stripe_payment": "Stripe Payment", "new_task": "New Task", "cancel": "Cancel", "empty_note": "Please Add Notes.", "show_google_map": "Show Google Map", "show_security_pin": "Show Security PIN", "sign-out-message": "Do you want to Sign Out?", "change_new_old_password": "New Password should not be same as Old Password", "change_new_confirm_password": "New Password & Confirm New Password did not match", "password_changed": "Password changed successfully", "flutter_boilerplate": "Flutter Boilerplate", "enable_location": "Enable Location", "enable_location_description": "Please Enable GPS from device Settings to use this functionality.", "close": "Close", "choose_image": "Choose Image", "validation": {"fill_required_fields": "Fill Required Fields"}, "navigation": {"drawer_navigation": "Drawer Navigation", "top_tab_navigation": "Top Bar Navigation", "bottom_tab_navigation": "Bottom Tab Navigation"}, "delete": "Delete", "delete_task": "Do you want to delete the task?", "google_maps": "Google Maps", "settings": "Settings", "appearance": "Appearance", "messaging": "Messaging", "profile": "Profile", "apiStatus": {"status400Error": "400 Bad Request, Please try again.", "status401Error": "401 Unauthorised, Please check your credentials try again.", "status404Error": "404 Not Found, Please try again.", "status408Error": "408 Request Timeout, Please try again.", "status500Error": "500 Internal Server Error occurred. Please try again.", "status504Error": "504 Gateway Timeout. Please try again.", "status505Error": "505 HTTP Version not supported. Please try again.", "statusNullError": "Something went wrong. Trouble in getting response from server. Try after sometime."}, "notifications": "Notifications", "security_status": "Security Status", "calling": "Calling", "social": "Social", "app_info": "App Info", "help_feedback": "Help & Feedback", "about": "About"}