<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>UIBackgroundModes</key>
		<array>
			<string>location</string>
		</array>
		<key>NSFaceIDUsageDescription</key>
		<string>authentication for face id</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>This app needs access to location when in use</string>
		<key>NSLocationAlwaysUsageDescription</key>
		<string>This app needs access to location when in the background</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>This app needs access to location when open</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>Need to upload image</string>
		<key>NSCameraUsageDescription</key>
		<string>Need to upload image</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>Need to upload image</string>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>Auction</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>Auction</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSRequiresIPhoneOS</key>
		<true />
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false />
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true />
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true />
		<key>CFBundleLocalizations</key>
		<array>
			<string>en</string>
			<string>hi</string>
		</array>
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsArbitraryLoads</key>
			<true />
		</dict>
		<key>FacebookAppID</key>
		<string>616638203906216</string>
		<key>FacebookClientToken</key>
		<string>CLIENT-TOKEN</string>
		<key>FacebookDisplayName</key>
		<string>Auction</string>
		<key>LSSupportsOpeningDocumentsInPlace</key>
		<true />
		<key>UIFileSharingEnabled</key>
		<true />
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>fbapi</string>
			<string>fb-messenger-share-api</string>
		</array>
	</dict>
</plist>
