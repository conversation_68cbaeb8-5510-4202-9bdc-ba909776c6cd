'use strict';
module.exports = {
    up: function (queryInterface, Sequelize) {
        return queryInterface.createTable('biddings', {
            id: {
                allowNull: false,
                primaryKey: true,
                type: Sequelize.UUID
            },
            player_id: {
                type: Sequelize.UUID,
                allowNull: false,
                references: {
                    model: 'players',
                    key: 'id'
                }
            },
            highest_bidder: {
                type: Sequelize.UUID,
                allowNull: false,
                references: {
                    model: 'users',
                    key: 'id'
                }
            },
            highest_bid: {
                type: Sequelize.INTEGER,
                allowNull: false
            },
            createdAt: {
                allowNull: false,
                type: Sequelize.DATE
            },
            updatedAt: {
                allowNull: false,
                type: Sequelize.DATE
            }
        });
    },
    down: function (queryInterface) {
        return queryInterface.dropTable('biddings');
    }
};
