'use strict';
module.exports = {
    up: function (queryInterface, Sequelize) {
        return queryInterface.createTable('players', {
            id: {
                allowNull: false,
                primaryKey: true,
                type: Sequelize.UUID
            },
            name: {
                type: Sequelize.STRING,
                allowNull: false
            },
            team_id: {
                type: Sequelize.INTEGER,
                allowNull: false,
                references: {
                    model: 'teams',
                    key: 'id'
                }
            },
            type_id: {
                type: Sequelize.INTEGER,
                allowNull: false,
                references: {
                    model: 'player_types',
                    key: 'id'
                }
            },
            base_price: {
                type: Sequelize.INTEGER
            },
            buy_price: {
                type: Sequelize.INTEGER
            },
            status: {
                type: Sequelize.ENUM('bought', 'not-bought', 'unsold'),
                allowNull: false,
                defaultValue: 'not-bought'
            },
            createdAt: {
                allowNull: false,
                type: Sequelize.DATE
            },
            updatedAt: {
                allowNull: false,
                type: Sequelize.DATE
            }
        });
    },
    down: function (queryInterface) {
        return queryInterface.dropTable('players');
    }
};
