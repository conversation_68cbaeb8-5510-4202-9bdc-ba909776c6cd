'use strict';
module.exports = {
    up: function (queryInterface, Sequelize) {
        return queryInterface.createTable('auction_details', {
            id: {
                type: Sequelize.UUID,
                primaryKey: true
            },
            max_batsman: {
                type: Sequelize.INTEGER,
                allowNull: false
            },
            max_bowler: {
                type: Sequelize.INTEGER,
                allowNull: false
            },
            max_wicketkeeper: {
                type: Sequelize.INTEGER,
                allowNull: false
            },
            assigned_amount: {
                type: Sequelize.INTEGER,
                allowNull: false
            },
            createdAt: {
                type: Sequelize.DATE,
                allowNull: false
            },
            updatedAt: {
                allowNull: false,
                type: Sequelize.DATE
            }
        });
    },
    down: function (queryInterface) {
        return queryInterface.dropTable('auction_details');
    }
};
