const { v4: uuidv4 } = require('uuid');

('use strict');
module.exports = {
  async up(queryInterface) {
    await queryInterface.bulkInsert('users', [
      {
        id: uuidv4(),
        name: '<PERSON>',
        phone: '9876543210',
        password:
          '$2a$10$fxjozkIVjqsbJuF2QMktCumIKbTA0zUNPr1r//231ZXVXqgSzbqMm',
        remaining_balance: 0,
        role: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: uuidv4(),
        name: '<PERSON>',
        phone: '9999999999',
        password:
          '$2a$10$ewhXEcr1jK0fKUfvDyvDWufHLOwnOLy1tarY8L3y2U2ks5rsHgFmm',
        remaining_balance: 0,
        role: 2,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: uuidv4(),
        name: '<PERSON>',
        phone: '8888888888',
        password:
          '$2a$10$ewhXEcr1jK0fKUfvDyvDWufHLOwnOLy1tarY8L3y2U2ks5rsHgFmm',
        remaining_balance: 0,
        role: 2,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Alice Smith',
        phone: '7777777777',
        password:
          '$2a$10$ewhXEcr1jK0fKUfvDyvDWufHLOwnOLy1tarY8L3y2U2ks5rsHgFmm',
        remaining_balance: 0,
        role: 2,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: uuidv4(),
        name: 'James Taylor',
        phone: '6666666666',
        password:
          '$2a$10$ewhXEcr1jK0fKUfvDyvDWufHLOwnOLy1tarY8L3y2U2ks5rsHgFmm',
        remaining_balance: 0,
        role: 2,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Deepak Gupta',
        phone: '5555555555',
        password:
          '$2a$10$ewhXEcr1jK0fKUfvDyvDWufHLOwnOLy1tarY8L3y2U2ks5rsHgFmm',
        remaining_balance: 0,
        role: 2,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Sanjay Yadav',
        phone: '4444444444',
        password:
          '$2a$10$ewhXEcr1jK0fKUfvDyvDWufHLOwnOLy1tarY8L3y2U2ks5rsHgFmm',
        remaining_balance: 0,
        role: 2,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Arun Reddy',
        phone: '3333333333',
        password:
          '$2a$10$ewhXEcr1jK0fKUfvDyvDWufHLOwnOLy1tarY8L3y2U2ks5rsHgFmm',
        remaining_balance: 0,
        role: 2,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Anita Mishra',
        phone: '2222222222',
        password:
          '$2a$10$ewhXEcr1jK0fKUfvDyvDWufHLOwnOLy1tarY8L3y2U2ks5rsHgFmm',
        remaining_balance: 0,
        role: 2,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Neha Yadav',
        phone: '1111111111',
        password:
          '$2a$10$ewhXEcr1jK0fKUfvDyvDWufHLOwnOLy1tarY8L3y2U2ks5rsHgFmm',
        remaining_balance: 0,
        role: 2,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Vrushti Shah',
        phone: '0000000000',
        password:
          '$2a$10$ewhXEcr1jK0fKUfvDyvDWufHLOwnOLy1tarY8L3y2U2ks5rsHgFmm',
        remaining_balance: 0,
        role: 2,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ]);
  },

  async down(queryInterface) {
    await queryInterface.bulkDelete('users', null, {});
  },
};
