const fs = require('fs');
const parse = require('csv-parser');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

('use strict');
module.exports = {
  async up(queryInterface, Sequelize) {
    const filePath = path.join(__dirname, '../data/players_list.csv');
    fs.createReadStream(filePath)
      .pipe(parse({ delimiter: ',' }))
      .on('data', async (row) => {
        const team = await queryInterface.sequelize.query('SELECT id FROM teams WHERE name = :teamName', {
          replacements: { teamName: row.team },
          type: Sequelize.QueryTypes.SELECT
        });
        const type = await queryInterface.sequelize.query('SELECT id FROM player_types WHERE type = :type', {
          replacements: { type: row.type },
          type: Sequelize.QueryTypes.SELECT
        });
        queryInterface.bulkInsert('players', [
          {
            id: uuidv4(),
            name: row.players_name,
            team_id: team[0].id,
            type_id: type[0].id,
            base_price: row.base_price,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ]);
      });
  },

  async down(queryInterface) {
    await queryInterface.bulkDelete('players', null, {});
  }
};
