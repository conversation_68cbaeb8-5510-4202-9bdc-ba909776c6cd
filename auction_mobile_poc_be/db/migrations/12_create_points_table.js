'use strict';
module.exports = {
  up: function (queryInterface, Sequelize) {
    return queryInterface.createTable('points', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.UUID
      },
      match_type: {
        type: Sequelize.STRING,
        allowNull: false
      },
      match_name: {
        type: Sequelize.STRING,
        allowNull: false
      },
      player_name: {
        allowNull: false,
        type: Sequelize.STRING
      },
      player_team: {
        type: Sequelize.STRING
      },
      player_recent_points: {
        type: Sequelize.INTEGER
      },
      match_status: {
        allowNull: false,
        type: Sequelize.STRING
      },
      match_date: {
        type: Sequelize.STRING
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });
  },
  down: function (queryInterface) {
    return queryInterface.dropTable('points');
  }
};
