sonar.host.url=https://quality.growexx.com/

sonar.jdbc.username=sonar
sonar.jdbc.password=sonar


sonar.projectKey=Auction-Mobile-BE
sonar.projectName=Auction-Mobile-BE
sonar.projectVersion=1.0

sonar.javascript.globals=DB_CONNECTION,CONSOLE_LOGGER,CONSTANTS,MESSAGES,MOMENT
sonar.sources=./
sonar.language=js
sonar.exclusions=crons/**,server/util/logger.js,**migrate-mongo-config.js,**/node_modules/**,**/coverage/**,**/test/**,**/jsdocs/**,**/etc/**,**/migrations/**,**Swagger.js,server/util/country.js,server/util/currency.js,server/util/timeZone.js,server/util/languageISO.js,util/http-status.js,**/*.html
sonar.javascript.lcov.reportPaths=./coverage/lcov.info
sonar.zaproxy.reportPath=target/zap-report/security_report.xml
sonar.zaproxy.report.dir=target/zap-report
sonar.zaproxy.htmlReportPath=target/zap-report/security_report.html
