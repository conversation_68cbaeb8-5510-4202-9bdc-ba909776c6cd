
# Auction Mobile BE

This is repository for Auction Mobile backend.

## Prerequisites

Before you begin, ensure you have met the following requirements:

- **Node:** Your development environment should have node installed. You can download it from [here](https://nodejs.org/en/downloadl).
- **npm:** This project relies on npm (Node Package Manager) to manage dependencies and run scripts. npm is typically included with Node.js installation, so there's no need for a separate installation.
- **IDE:** We recommend using [Visual Studio Code](https://code.visualstudio.com/download) as your development IDE.
- **Git:** You'll need Git to clone the project repository and manage version control.

Ensure you have the following software and versions installed:

- **Node:** >=18.15.0
- **npm:** >=9.6.5

## Configure app

Create an environment file with extension ```.env```.

- cp ```testing.env``` as ```YOUR_ENV.env```
- Change DB_NAME
- Change DB_HOST
- If you have DB Username add ```DB_USERNAME={VALUE}``` and ```DB_PASSWORD={VALUE}``` to your env file.
- Similarly change other fields as per your need.

# Running the application
- Clone the repository: git clone https://github.com/growexx/auction_mobile_poc_be
- Navigate to the project directory.
- Install dependencies: npm i
- start server: npm run start:dev