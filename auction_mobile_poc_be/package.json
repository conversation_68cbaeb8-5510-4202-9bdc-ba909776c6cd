{"name": "auction-mobile-be", "version": "1.0.0", "description": "Auction Mobile Backend Phase 1", "main": "index.js", "scripts": {"prestart": "NODE_ENV=local node_modules/.bin/sequelize db:migrate", "prestart:dev": "NODE_ENV=development node_modules/.bin/sequelize db:migrate", "start:dev": "NODE_ENV=development node index.js", "prestart:stg": "NODE_ENV=demo node_modules/.bin/sequelize db:migrate", "start:stg": "NODE_ENV=demo node index.js", "prestart:prod": "NODE_ENV=production node_modules/.bin/sequelize db:migrate", "start:prod": "NODE_ENV=production node index.js", "pretest": "NODE_ENV=testing node_modules/.bin/sequelize db:migrate", "start": "NODE_ENV=local nodemon .", "test": "NODE_ENV=testing nyc mocha test/alltests.js", "jsdoc": "./node_modules/.bin/jsdoc server/* -r  --destination jsdocs/jsdocs", "commit": "git cz"}, "author": "Growexx", "license": "ISC", "dependencies": {"aws-sdk": "^2.1060.0", "axios": "^0.26.1", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "compression": "^1.7.4", "cookie-parser": "^1.4.3", "cors": "^2.8.3", "csv-parser": "^3.0.0", "csvtojson": "^2.0.10", "dotenv": "^11.0.0", "eventsource": "^1.1.0", "express": "^4.15.3", "helmet": "^3.21.3", "i18n": "^0.8.3", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^8.2.1", "lodash": "^4.17.21", "method-override": "^2.3.9", "moment": "^2.19.2", "morgan": "^1.9.1", "multer": "^1.4.4", "node-xlsx": "^0.24.0", "nodemon": "^3.1.9", "os-utils": "^0.0.14", "pg": "^8.7.1", "pg-hstore": "^2.3.4", "promise": "^8.1.0", "sequelize": "^6.12.5", "sequelize-cli": "^6.3.0", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.1", "swagger-ui-express": "^4.1.6", "uuid": "^9.0.1", "websocket": "^1.0.34"}, "devDependencies": {"chai": "^4.3.4", "chai-http": "^4.3.0", "eslint": "^8.6.0", "jsdoc": "^3.6.6", "mocha": "^8.3.2", "nyc": "^15.1.0", "sinon": "^10.0.0", "source-map-support": "^0.5.19", "supertest": "^6.1.3"}, "nyc": {"lines": 5, "statements": 5, "functions": 5, "branches": 5, "check-coverage": true, "exclude": ["node_modules", "**/test/**", "**/coverage/**", "**/migrations/**", "jsdocs", "getEnvs.js", ".eslintrc.js", "migrate-mongo-config.js", "util/country.js", "util/currency.js", "util/timeZone.js", "util/languageISO.js", "util/http-status.js"], "reporter": ["lcov", "html"], "cache": true, "all": true}, "directories": {"test": "test"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}}