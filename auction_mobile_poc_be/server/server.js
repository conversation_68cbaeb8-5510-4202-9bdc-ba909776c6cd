/**
 * @name Server Configuration
 */

const compression = require('compression');
const express = require('express');
const cookieParser = require('cookie-parser');
const app = express();
const authRoutes = require('./routes/authRoutes');
const userRoutes = require('./routes/userRoutes');
const pointsRoutes = require('./routes/pointsRoutes');
const testRoutes = require('./routes/testRoutes');
const dbRoutes = require('./routes/dbRoutes');
const bodyParser = require('body-parser');
const swaggerUi = require('swagger-ui-express');
const swaggerDoc = require('swagger-jsdoc');
const swaggerDef = require('./public/swagger');
const cors = require('cors');
const methodOverride = require('method-override');
const i18n = require('i18n');
const morgan = require('morgan');
const helmet = require('helmet');
const http = require('http');
const { Server } = require('socket.io');
const Utils = require('./util/utilFunctions');

// initialize a simple http server
const server = http.createServer(app);

// initialize Socket.IO
const io = new Server(server, {
  cors: {
    origin: '*',
    methods: ['GET', 'POST']
  }
});

const Socket = require('./util/socket');

// Global Variables
global.CONSOLE_LOGGER = require('./util/logger');
global.CONSTANTS = require('./util/constants');
global.MESSAGES = require('./locales/en.json');
global.MOMENT = require('moment');
global._ = require('lodash');
global.IO = io;

if (process.env.LOCAL === 'true') {
  app.use(express.static('../jsdocs/jsdocs'));
  app.use('/auth/coverage', express.static(`${__dirname}/../coverage/lcov-report`));
}

// Configure i18n for multilingual
i18n.configure({
  locales: ['en'],
  directory: `${__dirname}/locales`,
  extension: '.json',
  prefix: '',
  logDebugFn(msg) {
    if (process.env.LOCAL === 'true') {
      CONSOLE_LOGGER.debug(`i18n::${CONSTANTS.LOG_LEVEL}`, msg);
    }
  }
});

app.use(compression());
app.use(helmet());
app.use(i18n.init);
app.use(cookieParser());

app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));
app.use(bodyParser.json({ limit: '50mb', extended: true }));

app.use(
  cors({
    origin: true,
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    credentials: true,
    exposedHeaders: ['x-auth-token']
  })
);

app.use(morgan('dev'));
app.use(methodOverride());

// Landing Page
app.get('/', (req, res) => {
  res.send({
    status: 'ok',
    date: MOMENT()
  });
});
app.use('/auth', authRoutes);
app.use('/user', userRoutes);
app.use('/points', pointsRoutes);
app.use('/test', testRoutes);
app.use('/db', dbRoutes);

const spec = swaggerDoc(swaggerDef);

if (process.env.NODE_ENV !== 'production') {
  app.use('/api-docs/', swaggerUi.serve, swaggerUi.setup(spec));
}

io.on('connection', (socket) => {
  console.log('🚀🚀🚀 ~ SERVER ~ connection ~ ===== NEW SOCKET CONNECTION =====');
  console.log('🚀🚀🚀 ~ SERVER ~ connection ~ Timestamp:', new Date().toISOString());
  console.log('🚀🚀🚀 ~ SERVER ~ connection ~ Original socket ID:', socket.id);
  console.log('🚀🚀🚀 ~ SERVER ~ connection ~ Socket connected:', socket.connected);
  console.log('🚀🚀🚀 ~ SERVER ~ connection ~ Socket handshake:', JSON.stringify(socket.handshake, null, 2));
  console.log('🚀🚀🚀 ~ SERVER ~ connection ~ Client IP:', socket.handshake.address);
  console.log('🚀🚀🚀 ~ SERVER ~ connection ~ User agent:', socket.handshake.headers['user-agent']);
  console.log('🚀🚀🚀 ~ SERVER ~ connection ~ Environment:', process.env.NODE_ENV || 'not set');
  console.log('🚀🚀🚀 ~ SERVER ~ connection ~ Server hostname:', require('os').hostname());

  global.SOCKET = socket;
  const newSocketId = Utils.getUniqueID();
  socket.id = newSocketId;
  console.log('🚀🚀🚀 ~ SERVER ~ connection ~ Assigned new socket ID:', socket.id);
  console.log('🚀🚀🚀 ~ SERVER ~ connection ~ Socket.IO client connected with ID:', socket.id);

  // Set initial connection state
  socket.isAlive = true;
  socket.isDisconnected = false;
  console.log('🚀🚀🚀 ~ SERVER ~ connection ~ Initial connection state set');
  console.log('🚀🚀🚀 ~ SERVER ~ connection ~ Socket isAlive:', socket.isAlive);
  console.log('🚀🚀🚀 ~ SERVER ~ connection ~ Socket isDisconnected:', socket.isDisconnected);

  socket.on('registerUser', async (messageBody) => {
    console.log('🚀🚀🚀 ~ SERVER ~ registerUser event ~ ===== REGISTER USER EVENT RECEIVED =====');
    console.log('🚀🚀🚀 ~ SERVER ~ registerUser event ~ Timestamp:', new Date().toISOString());
    console.log('🚀🚀🚀 ~ SERVER ~ registerUser event ~ Socket ID:', socket.id);
    console.log('🚀🚀🚀 ~ SERVER ~ registerUser event ~ Message body:', JSON.stringify(messageBody, null, 2));
    console.log('🚀🚀🚀 ~ SERVER ~ registerUser event ~ Socket connected:', socket.connected);
    console.log('🚀🚀🚀 ~ SERVER ~ registerUser event ~ Socket disconnected:', socket.disconnected);
    console.log('🚀🚀🚀 ~ SERVER ~ registerUser event ~ Environment:', process.env.NODE_ENV || 'not set');
    console.log('🚀🚀🚀 ~ SERVER ~ registerUser event ~ Server hostname:', require('os').hostname());

    try {
      console.log('🚀🚀🚀 ~ SERVER ~ registerUser event ~ Calling Socket.registerUser...');
      const result = await Socket.registerUser(io, socket, messageBody);
      console.log('🚀🚀🚀 ~ SERVER ~ registerUser event ~ Socket.registerUser completed successfully');
      console.log('🚀🚀🚀 ~ SERVER ~ registerUser event ~ Result type:', typeof result);
      console.log('🚀🚀🚀 ~ SERVER ~ registerUser event ~ ===== REGISTER USER EVENT COMPLETED =====');
    } catch (error) {
      console.log('🚀🚀🚀 ~ SERVER ~ registerUser event ~ ===== REGISTER USER EVENT FAILED =====');
      console.log('🚀🚀🚀 ~ SERVER ~ registerUser event ~ Error timestamp:', new Date().toISOString());
      console.log('🚀🚀🚀 ~ SERVER ~ registerUser event ~ Error type:', typeof error);
      console.log('🚀🚀🚀 ~ SERVER ~ registerUser event ~ Error message:', error.message);
      console.log('🚀🚀🚀 ~ SERVER ~ registerUser event ~ Error stack:', error.stack);
      console.log('🚀🚀🚀 ~ SERVER ~ registerUser event ~ Error object:', JSON.stringify(error, Object.getOwnPropertyNames(error), 2));

      CONSOLE_LOGGER.error('registerUser error:', error);

      console.log('🚀🚀🚀 ~ SERVER ~ registerUser event ~ Emitting error response...');
      const errorResponse = {
        action: 'error',
        message: error.message || 'Authentication failed',
        statusCode: error.statusCode || 400
      };
      console.log('🚀🚀🚀 ~ SERVER ~ registerUser event ~ Error response:', JSON.stringify(errorResponse, null, 2));

      socket.emit('error', errorResponse);
      console.log('🚀🚀🚀 ~ SERVER ~ registerUser event ~ Error response emitted');
      console.log('🚀🚀🚀 ~ SERVER ~ registerUser event ~ ===== REGISTER USER EVENT ENDED WITH ERROR =====');
    }
  });
  socket.on('disconnectUser', (messageBody) => {
    try {
      Socket.disconnectClient(io, socket, messageBody);
    } catch (error) {
      CONSOLE_LOGGER.error('disconnectUser error:', error);
      socket.emit('error', {
        action: 'error',
        message: error.message || 'Failed to disconnect',
        statusCode: error.statusCode || 400
      });
    }
  });
  socket.on('startAuction', async (messageBody) => {
    try {
      try {
        await Socket.registerUser(io, socket, messageBody);
      } catch (error) {
        console.log('startAuction registerUser error:', error);
      }
      await Socket.startAuction(io, socket, messageBody);
    } catch (error) {
      CONSOLE_LOGGER.error('startAuction error:', error);
      socket.emit('error', {
        action: 'error',
        message: error.message || 'Failed to start auction',
        statusCode: error.statusCode || 400
      });
    }
  });
  socket.on('addBid', (messageBody) => {
    try {
      Socket.addBid(io, socket, messageBody);
    } catch (error) {
      CONSOLE_LOGGER.error('addBid error:', error);
      socket.emit('error', {
        action: 'error',
        message: error.message || 'Failed to add bid',
        statusCode: error.statusCode || 400
      });
    }
  });
  socket.on('getNewPlayer', async (data) => {
    try {
      // Optionally register the user first
      try {
        await Socket.registerUser(io, socket, data);
      } catch (error) {
        console.log('registerUser error:', error);
        // Continue even if registration fails
      }

      await Socket.getAnotherPlayer(io, socket, data);
    } catch (error) {
      CONSOLE_LOGGER.error('getNewPlayer error:', error);
      socket.emit('error', {
        action: 'error',
        message: error.message || 'Failed to get new player',
        statusCode: error.statusCode || 400
      });
    }
  });
  socket.on('reconnect', (messageBody) => {
    try {
      Socket.reConnect(io, socket, messageBody);
    } catch (error) {
      CONSOLE_LOGGER.error('reconnect error:', error);
      socket.emit('error', {
        action: 'error',
        message: error.message || 'Failed to reconnect',
        statusCode: error.statusCode || 400
      });
    }
  });
  socket.on('auctionStatus', (messageBody) => {
    try {
      Socket.auctionStatus(io, socket, messageBody);
    } catch (error) {
      CONSOLE_LOGGER.error('auctionStatus error:', error);
      socket.emit('error', {
        action: 'error',
        message: error.message || 'Failed to get auction status',
        statusCode: error.statusCode || 400
      });
    }
  });
  socket.on('auctionCompleted', async (data) => {
    try {
      // Register the user first
      try {
        await Socket.registerUser(io, socket, data);
      } catch (error) {
        console.log('registerUser error:', error);
        // Continue even if registration fails, as the user might already be registered
      }

      await Socket.auctionCompleted(io, socket, data);
    } catch (error) {
      CONSOLE_LOGGER.error('auctionCompleted error:', error);
      socket.emit('error', {
        action: 'error',
        message: error.message || 'Failed to complete auction',
        statusCode: error.statusCode || 400
      });
    }
  });
  socket.on('getUpcomingPlayers', (messageBody) => {
    try {
      Socket.handleUpcomingPlayersRequest(io, socket, messageBody);
    } catch (error) {
      CONSOLE_LOGGER.error('getUpcomingPlayers error:', error);
      socket.emit('error', {
        action: 'error',
        message: error.message || 'Failed to get upcoming players',
        statusCode: error.statusCode || 400
      });
    }
  });
  socket.on('lastCall', async (messageBody) => {
    try {
      try {
        await Socket.registerUser(io, socket, messageBody);
      } catch (error) {
        console.log('lastCall ~ registerUser error:', error);
      }
      await Socket.lastCall(io, socket, messageBody);
    } catch (error) {
      CONSOLE_LOGGER.error('lastCall error:', error);
      socket.emit('error', {
        action: 'error',
        message: error.message || 'Failed to make last call',
        statusCode: error.statusCode || 400
      });
    }
  });
  socket.on('getUserDetails', async (messageBody) => {
    try {
      try {
        await Socket.registerUser(io, socket, messageBody);
      } catch (error) {
        console.log('getUserDetails registerUser error:', error);
      }
      await Socket.getUserDetails(socket);
    } catch (error) {
      CONSOLE_LOGGER.error('getUserDetails error:', error);
      socket.emit('error', {
        action: 'error',
        message: error.message || 'Failed to get user details',
        statusCode: error.statusCode || 400
      });
    }
  });
  socket.on('getAllUserDetails', async (messageBody) => {
    try {
      try {
        await Socket.registerUser(io, socket, messageBody);
      } catch (error) {
        console.log('getUserDetails registerUser error:', error);
      }
      await Socket.getAllUserDetails(io, socket, messageBody);
    } catch (error) {
      CONSOLE_LOGGER.error('getAllUserDetails error:', error);
      socket.emit('error', {
        action: 'error',
        message: error.message || 'Failed to get all user details',
        statusCode: error.statusCode || 400
      });
    }
  });
  socket.on('disconnect', (reason) => {
    CONSOLE_LOGGER.info(`Client disconnected: ${socket.id}, reason: ${reason}`);
    socket.isAlive = false;
    Socket.disconnectClient(io, socket);
  });
  socket.on('error', (error) => {
    CONSOLE_LOGGER.info('Client triggered error:', error);
    socket.isAlive = false;
  });
});

process.setMaxListeners(0);
module.exports = server;
