/**
 * Test Routes
 */
const express = require('express');
const router = express.Router();
const path = require('path');
const crypt = require('../util/crypt');
const db = require('../models');
const User = db.user;
const jwt = require('jsonwebtoken');

/**
 * @swagger
 * /test/auction:
 *   get:
 *     description: Test page for auction functionality
 *     responses:
 *       200:
 *         description: Returns the test auction page
 */
router.get('/auction', (req, res) => {
    res.sendFile(path.join(__dirname, '../public/test-auction.html'));
});

/**
 * @swagger
 * /test/check-token:
 *   post:
 *     description: Check if a token is valid and belongs to an admin
 *     parameters:
 *       - name: token
 *         description: JWT token
 *         in: body
 *         required: true
 *         type: string
 *     responses:
 *       200:
 *         description: Returns token information
 *       401:
 *         description: Invalid token
 */
router.post('/check-token', async (req, res) => {
    try {
        const { token } = req.body;
        
        if (!token) {
            return res.status(400).json({ message: 'Token is required' });
        }
        
        // Decode the token
        const decodedJwt = jwt.decode(token, { complete: true });
        
        if (!decodedJwt || !decodedJwt.payload) {
            return res.status(401).json({ message: 'Invalid token format' });
        }
        
        const decodedJwtPayload = decodedJwt.payload;
        
        // Check if token is expired
        const compareDate = Math.floor(Date.now() / 1000);
        if (decodedJwtPayload.exp < compareDate) {
            return res.status(401).json({ message: 'Token has expired' });
        }
        
        // Find the user
        const user = await User.findByPk(decodedJwtPayload.id);
        
        if (!user) {
            return res.status(401).json({ message: 'User not found' });
        }
        
        // Check if user is admin
        const isAdmin = user.role === 1;
        
        res.json({
            valid: true,
            isAdmin,
            userId: user.id,
            name: user.name,
            phone: user.phone,
            role: user.role
        });
    } catch (error) {
        console.error('Error checking token:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
});

/**
 * @swagger
 * /test/generate-admin-token:
 *   post:
 *     description: Generate an admin token for testing
 *     parameters:
 *       - name: phone
 *         description: Admin phone number
 *         in: body
 *         required: true
 *         type: string
 *       - name: password
 *         description: Admin password
 *         in: body
 *         required: true
 *         type: string
 *     responses:
 *       200:
 *         description: Returns a JWT token
 *       401:
 *         description: Authentication failed
 */
router.post('/generate-admin-token', async (req, res) => {
    try {
        const { phone, password } = req.body;
        
        console.log('Attempting to find user with phone:', phone);
        
        // Find user by phone
        const user = await User.findOne({ where: { phone } });
        
        if (!user) {
            return res.status(401).json({ message: 'User not found' });
        }
        
        // Check if user is admin (role = 1)
        if (user.role !== 1) {
            return res.status(401).json({ message: 'User is not an admin' });
        }
        
        // Verify password
        const isMatch = await crypt.comparePassword(password, user.password);
        if (!isMatch) {
            return res.status(401).json({ message: 'Invalid password' });
        }
        
        // Generate JWT token
        const token = await crypt.getUserAccessToken(user);
        
        res.json({ token: token.token });
    } catch (error) {
        console.error('Error generating admin token:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
});

module.exports = router; 