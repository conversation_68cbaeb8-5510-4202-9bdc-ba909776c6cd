/**
 * Database Routes
 */
const express = require('express');
const router = express.Router();
const dbController = require('../services/db/dbController');
const { authenticateJWT, isAdmin } = require('../middleware/authMiddleware');
const AclMiddleWare = require('../middleware/acl');

/**
 * @swagger
 * /db/reset:
 *   post:
 *     description: Reset/clean the entire database (admin only)
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Database reset completed successfully
 *       401:
 *         description: Unauthorized - not authenticated or not an admin
 *       500:
 *         description: Server error during database reset
 */
router.post('/reset', authenticateJWT, AclMiddleWare, isAdmin, dbController.resetDatabase);

module.exports = router; 