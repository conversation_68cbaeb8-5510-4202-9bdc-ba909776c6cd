const router = require('express').Router();
const AuthMiddleWare = require('../middleware/auth');
const AclMiddleWare = require('../middleware/acl');
const pointsController = require('../services/points/pointsController');


router.post('/sync-player-points', AuthMiddleWare, AclMiddleWare, pointsController.syncPlayerPoints);
router.post('/total-points', AuthMiddleWare, AclMiddleWare, pointsController.getTotalPoints);

module.exports = router;
