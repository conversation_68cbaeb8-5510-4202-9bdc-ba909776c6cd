const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
chai.use(chaiHttp);
const jwt = require('jsonwebtoken');
const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};

// Invalid Token
const invalidToken = {
    token: ''
};

const noAccessUser = {
    id: '87a688d1-0134-4890-9718-62e37d3b3547',
    phone: '9876543210'
};

const requestPayloadNoAccess = {
    token: jwt.sign(noAccessUser, process.env.JWT_ACCESS_SECRET, tokenOptionalInfo)
};

// User Token
const user = {
    id: '74639f5e-4822-44cf-8732-c4cff6919432',
    phone: '9999999999'
};
const requestPayloadUser = {
    token: jwt.sign(user, process.env.JWT_ACCESS_SECRET, tokenOptionalInfo)
};

describe('User Profile get', () => {
    try {
        it('Check invalid token ', (done) => {
            request(process.env.BASE_URL)
                .get('/user/details')
                .set({ Authorization: invalidToken.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('Get user details', (done) => {
            request(process.env.BASE_URL)
                .get('/user/details')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });

        it('Validate if user has access or not', (done) => {
            request(process.env.BASE_URL)
                .get('/user/details')
                .set({ Authorization: requestPayloadNoAccess.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.body.status, 0);
                    assert.equal(res.statusCode, 406);
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});
