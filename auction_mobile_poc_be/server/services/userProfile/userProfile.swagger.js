/**
 *  routes and schema for UserProfile
 */

/**
 * @openapi
 *  /user/details:
 *      get:
 *          security:
 *              - bearerAuth: []
 *          tags: [User]
 *          summary: User Details
 *          responses:
 *              200:
 *                  description: User get details
 *                  content:
 *                      application/json:
 *                          schema:
 *                              $ref: '#/components/schemas/successUserDetails'
 *              400:
 *                  description: Invalid Request
 *                  content:
 *                      application/json:
 *                          schema:
 *                              $ref: '#/components/schemas/validationError'
 *              401:
 *                  description: Unauthorised Access
 *                  content:
 *                      application/json:
 *                          schema:
 *                              $ref: '#/components/schemas/unauthorisedAccessUser'
 *              500:
 *                  description: internal server error
 *                  content:
 *                      application/json:
 *                          schema:
 *                              $ref: '#/components/schemas/unexpectedError'
 *
 *
 * /user/export-csv:
 *  get:
 *      security:
 *          - bearerAuth: []
 *      tags: [User]
 *      summary: export csv
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successLogin'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccess'
 *          401:
 *              description: User duplicate
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessLogin'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 *
 *
 * /user/import-csv:
 *  post:
 *      security:
 *          - bearerAuth: []
 *      tags: [User]
 *      summary: import csv
 *      description: |
 *          Import data from CSV file with the following features:
 *          - Team names are extracted from the first column (index 0)
 *          - Player names, types and base prices are processed from specific columns
 *          - User data (columns after Base Price):
 *              - First row: usernames
 *              - Second row: phone numbers
 *              - Third row: passwords
 *              - Fourth row: wallet amounts
 *          - Bid values are extracted from user columns where a player row has a value
 *          - Set replaceAll=true to clear existing data before import
 *          - Set replacePlayers=true to also replace player data
 *          - Set force_create_players=true to create all missing players (set to true by default)
 *      requestBody:
 *        required: true
 *        content:
 *          multipart/form-data:
 *            schema:
 *              type: object
 *              properties:
 *                file:
 *                  type: string
 *                  format: binary
 *                  description: CSV file to import (with teams, players, ownership, users, and bids)
 *                replaceAll:
 *                  type: string
 *                  enum: ['true', 'false']
 *                  description: If 'true', all existing data will be cleared before import
 *                replacePlayers:
 *                  type: string
 *                  enum: ['true', 'false']
 *                  description: If 'true', player data (names, types) will be replaced or created if missing
 *                force_create_players:
 *                  type: string
 *                  enum: ['true', 'false']
 *                  default: 'true'
 *                  description: If 'true' (default), all players in the CSV will be created even without owners
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          type: object
 *                          properties:
 *                              status:
 *                                  type: integer
 *                                  example: 1
 *                              data:
 *                                  type: object
 *                                  properties:
 *                                      success:
 *                                          type: boolean
 *                                          example: true
 *                                      message:
 *                                          type: string
 *                                          example: CSV data imported and database updated successfully
 *                                      replacedData:
 *                                          type: boolean
 *                                          example: true
 *                                      replacedPlayers:
 *                                          type: boolean
 *                                          example: true
 *                                      forceCreatePlayers:
 *                                          type: boolean
 *                                          example: true
 *                                      fileInfo:
 *                                          type: object
 *                                          description: Information about the imported CSV file
 *                                          properties:
 *                                              filename:
 *                                                  type: string
 *                                                  example: "players.csv"
 *                                                  description: Name of the uploaded file
 *                                              size:
 *                                                  type: integer
 *                                                  example: 10927
 *                                                  description: Size of the file in bytes
 *                                              rows:
 *                                                  type: integer
 *                                                  example: 222
 *                                                  description: Number of player records processed (excluding header rows)
 *                                      stats:
 *                                          type: object
 *                                          description: Statistics about the import process
 *                                          properties:
 *                                              playersCreated:
 *                                                  type: integer
 *                                                  example: 200
 *                                                  description: Number of new players created during import
 *                                              playersUpdated:
 *                                                  type: integer
 *                                                  example: 5
 *                                                  description: Number of existing players updated during import
 *                                              playersSkipped:
 *                                                  type: integer
 *                                                  example: 2
 *                                                  description: Number of players skipped due to errors or missing required data
 *                                              playersUnassigned:
 *                                                  type: integer
 *                                                  example: 180
 *                                                  description: Number of players created without an owner (available to be claimed)
 *                                              usersCreated:
 *                                                  type: integer
 *                                                  example: 3
 *                                                  description: Number of new user accounts created during import
 *                                              biddingsCreated:
 *                                                  type: integer
 *                                                  example: 15
 *                                                  description: Number of new bidding records created
 *                                              biddingsUpdated:
 *                                                  type: integer
 *                                                  example: 8
 *                                                  description: Number of existing bidding records updated
 *                                              transactionsCreated:
 *                                                  type: integer
 *                                                  example: 15
 *                                                  description: Number of new transaction records created
 *                                      columnIndices:
 *                                          type: object
 *                                          description: Indices of important columns detected in the CSV
 *                                          properties:
 *                                              playerNameIndex:
 *                                                  type: integer
 *                                                  example: 1
 *                                                  description: Column index where player names were found
 *                                              belongsToIndex:
 *                                                  type: integer
 *                                                  example: 2
 *                                                  description: Column index where ownership information was found
 *                                              playerTypeIndex:
 *                                                  type: integer
 *                                                  example: 4
 *                                                  description: Column index where player types were found
 *                                              basePriceIndex:
 *                                                  type: integer
 *                                                  example: 5
 *                                                  description: Column index where base prices were found
 *                                      totalPlayersProcessed:
 *                                          type: integer
 *                                          example: 207
 *                                          description: Total count of all players processed (created + updated + skipped, does not double-count unassigned players)
 *                              message:
 *                                  type: string
 *                                  example: Success
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */
