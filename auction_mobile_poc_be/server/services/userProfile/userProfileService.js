const User = require('../../models').user;
const Transaction = require('../../models').player_transaction;
const Player = require('../../models').player;
const PlayerType = require('../../models').player_type;
const Team = require('../../models').team;
const { sequelize } = require('../../models/index');
const db = require('../../models');
const Json2CsvParser = require('json2csv').Parser;
const QUERY_CONSTANTS = require('../../util/queryConstants');
const CONSTANTS = require('../../util/constants');
const Biddings = require('../../models').bidding;
const AuctionDetails = require('../../models').auction_details;
const xlsx = require('node-xlsx').default;
const Crypt = require('../../util/crypt');
/**
 * Class represents services for user Basic Profile.
 */
class UserProfileService {
  /**
   * @desc This function is being used to get user details
   * <AUTHOR>
   * @since 22/09/2023
   * @param {Object} req Request
   * @param {Object} user User
   */
  static async getUserDetails(req, user) {
    const userDetails = await User.findByPk(user.id, {
      include: [
        {
          model: Transaction,
          include: [
            {
              model: Player,
              include: [PlayerType, Team],
            },
          ],
        },
      ],
    });
    const returnObj = userDetails.dataValues;
    delete returnObj.password;
    returnObj.token = req.headers.authorization;
    return returnObj;
  }

  static async exportToCSV(req, res) {
    try {
      const [users] = await sequelize.query(
        QUERY_CONSTANTS.EXPORT_CSV_QUERY[CONSTANTS.GET_USERS]
      );

      if (!users || !users.length) {
        return res.status(404).json({
          status: 404,
          success: false,
          message: 'No users found for exporting data.',
        });
      }
  
      const [userData] = await sequelize.query(
        QUERY_CONSTANTS.EXPORT_CSV_QUERY[CONSTANTS.USER_DATA]
      );
  
      const userColumns = users
        .map(
          (user) =>
            `MAX(CASE WHEN ul.name = '${user.name}' AND pb.buyer_name = '${user.name}' THEN pb.highest_bid END) AS "${user.name}"`
        )
        .join(',\n  ');
  
      const [results] = await sequelize.query(
        QUERY_CONSTANTS.EXPORT_CSV_QUERY.soldPlayer(userColumns)
      );
  
      if (!results?.length) {
        return res.status(404).json({
          status: 404,
          success: false,
          message: 'No player data available to export.',
        });
      }
  
      const playerData = results;

      const userTable = userData.map((user) => ({
        Name: user.name,
        "Funds In Hand": user.funds_in_hand,
        "Funds Used": user.funds_used,
        Balance: user.balance,
      }));
  
    const playerTable = playerData.map((playerRow) => {
      const row = {
        Team: playerRow.team,
        PlayerName: playerRow['Players Name'],
        BelongsTo: playerRow['Belongs To'],
        BasePrice: playerRow['Base Price'],
        Type: playerRow.TYPE,
      };

      Object.keys(playerRow).forEach((key) => {
        if (!['team', 'Players Name', 'Belongs To', 'Base Price', 'TYPE'].includes(key)) {
          row[key] = playerRow[key];
        }
      });

      return row;
    });
  
      const userFields = Object.keys(userTable[0] || {});
      const playerFields = [
        'Team',
        'PlayerName',
        'BelongsTo',
        'BasePrice',
        'Type',
        ...Object.keys(playerData[0]).filter(
          (key) =>
            !['team', 'Players Name', 'Belongs To', 'Base Price', 'TYPE'].includes(key)
        ),
      ];  
      const csvParser1 = new Json2CsvParser({ fields: userFields });
      const csvParser2 = new Json2CsvParser({ fields: playerFields });
  
      const csvData1 = csvParser1.parse(userTable);
      const csvData2 = csvParser2.parse(playerTable);
  
      const combinedCsvData = `${csvData1}\n\n${csvData2}`;
  
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader(
        'Content-Disposition',
        'attachment; filename="user_player_data.csv"'
      );
      res.setHeader('Content-Length', Buffer.byteLength(combinedCsvData));
  
      return res.status(200).send(combinedCsvData);
    } catch (error) {
      console.error('Error fetching data for CSV export:', error);
      return res.status(500).json({
        status: 500,
        success: false,
        message: 'Error fetching data for export',
      });
    }
  }

  /**
   * @desc This function is being used to import data from csv and store it
   * <AUTHOR>
   * @since 20/01/2025
   * @param {Object} req Request
   */

  static async importFromCSV(req) {
    try {
      const { replaceAll, replacePlayers, force_create_players = 'true' } = req.body;
      console.log("Import parameters:", { 
        replaceAll, 
        replacePlayers,
        force_create_players,
        replaceAllIsTrue: replaceAll === 'true',
        replacePlayersIsTrue: replacePlayers === 'true',
        forceCreatePlayersIsTrue: force_create_players === 'true'
      });
      
      // Log the file being imported
      console.log(`Importing file: ${req.file.originalname}, size: ${req.file.size} bytes`);
      
      const fileBuffer = req.file.buffer;
      const sheets = xlsx.parse(fileBuffer, { raw: true });
      const rawData = sheets?.[0]?.data ?? [];

      console.log("DEBUG: Sheet data loaded:", sheets.length > 0 ? "Yes" : "No");
      console.log("DEBUG: First sheet rows:", rawData.length);
      console.log("DEBUG: Sheet names:", sheets.map(s => s.name));
      
      // Extract header rows (first 4 rows)
      const columnNames = rawData[0] || [];
      const phoneNumbers = rawData[1] || [];
      const passwords = rawData[2] || [];
      const walletAmounts = rawData[3] || [];

      // Print user data for debugging
      console.log("DEBUG: User column data:");
      for (let i = 5; i < columnNames.length; i++) {
        if (columnNames[i]) {
          console.log(`User column ${i}:`, {
            name: columnNames[i],
            phone: phoneNumbers[i] || "None",
            password: passwords[i] ? "****" : "None", // Don't log actual passwords
            wallet: walletAmounts[i] || 0
          });
        }
      }

      // Extract the actual player data (starting from row 5)
      const records = rawData.slice(4); // Skip the first 4 rows

      // Print first few rows for debugging
      console.log("DEBUG: Header row (column names):", columnNames);
      console.log("DEBUG: Total player records:", records.length);

      // If replaceAll is true, clear all relevant data first
      if (replaceAll === 'true') {
        CONSOLE_LOGGER.info('Clearing database before import...');
        
        try {
          // Instead of using a transaction for the whole process, we'll do each operation separately
          // This is safer in case one operation fails
          
          // Detect database dialect to use correct syntax
          const dialect = db.sequelize.getDialect();
          CONSOLE_LOGGER.info(`Database dialect detected: ${dialect}`);
          
          // Clear biddings
          CONSOLE_LOGGER.info('Deleting bidding records');
          if (dialect === 'postgres') {
            try {
              const biddingTable = db.bidding.tableName || 'bidding';
              await db.sequelize.query(`DELETE FROM "${biddingTable}"`, {});
            } catch (e) {
              CONSOLE_LOGGER.error(`Error deleting from bidding: ${e.message}`);
              await db.bidding.destroy({ where: {} });
            }
          } else {
            await db.bidding.destroy({ where: {} });
          }
          
          // Clear transactions
          CONSOLE_LOGGER.info('Deleting player_transaction records');
          if (dialect === 'postgres') {
            try {
              const transactionTable = db.player_transaction.tableName || 'player_transaction';
              await db.sequelize.query(`DELETE FROM "${transactionTable}"`, {});
            } catch (e) {
              CONSOLE_LOGGER.error(`Error deleting from player_transaction: ${e.message}`);
              await db.player_transaction.destroy({ where: {} });
            }
          } else {
            await db.player_transaction.destroy({ where: {} });
          }
          
          // Clear or update players
          if (replacePlayers === 'true') {
            CONSOLE_LOGGER.info('Deleting player records');
            if (dialect === 'postgres') {
              try {
                const playerTable = db.player.tableName || 'player';
                await db.sequelize.query(`DELETE FROM "${playerTable}"`, {});
              } catch (e) {
                CONSOLE_LOGGER.error(`Error deleting from player: ${e.message}`);
                await db.player.destroy({ where: {} });
              }
            } else {
              await db.player.destroy({ where: {} });
            }
          } else {
            // Otherwise just clear player buy_price
            CONSOLE_LOGGER.info('Clearing player buy_price');
            await db.player.update({ buy_price: null }, { where: {} });
          }
          
          // Reset user wallet amounts but preserve admin users
          CONSOLE_LOGGER.info('Resetting user wallet amounts');
          await db.user.update(
            { remaining_balance: 0, wallet_amount: 0 },
            { where: { role: { [db.Sequelize.Op.ne]: 1 } } }
          );
          
          CONSOLE_LOGGER.info('Database cleared successfully');
        } catch (error) {
          CONSOLE_LOGGER.error(`Error clearing database: ${error.message}`);
          throw new Error(`Failed to clear database: ${error.message}`);
        }
      }

      // Find the index of important columns - we need to scan the column names
      const basePriceIndex = columnNames.indexOf('Base Price');
      // Find PlayerName, BelongsTo and Type columns by searching for common headers
      let playerNameIndex = columnNames.indexOf('PlayerName');
      if (playerNameIndex === -1) playerNameIndex = columnNames.indexOf('Players Name');
      if (playerNameIndex === -1) playerNameIndex = columnNames.indexOf('Player Name');
      if (playerNameIndex === -1) playerNameIndex = 1; // Fallback

      let belongsToIndex = columnNames.indexOf('BelongsTo');
      if (belongsToIndex === -1) belongsToIndex = columnNames.indexOf('Belongs To');
      if (belongsToIndex === -1) belongsToIndex = columnNames.indexOf('Team');
      if (belongsToIndex === -1) belongsToIndex = 2; // Fallback

      let playerTypeIndex = columnNames.indexOf('Type');
      if (playerTypeIndex === -1) playerTypeIndex = columnNames.indexOf('PlayerType');
      if (playerTypeIndex === -1) playerTypeIndex = columnNames.indexOf('Player Type');
      if (playerTypeIndex === -1) playerTypeIndex = 4; // Fallback

      console.log("DEBUG: Important column indices:", {
        playerNameIndex,
        belongsToIndex,
        playerTypeIndex,
        basePriceIndex
      });

      // Extract relevant columns after 'Base Price'
      const relevantColumns = columnNames.slice(basePriceIndex + 1);
      console.log("DEBUG: Relevant columns:", relevantColumns);

      // For debugging, show a sample of rows to be processed
      console.log("DEBUG: Total records:", records.length);
      if (records.length > 5) {
        console.log("DEBUG: Sample record at index 5:", records[5]);
      }

      // Set a debugging flag to see detailed processing for specific rows
      const debugEveryNthRow = 50; // Debug every 50th row in detail

      // Track stats for the summary
      const stats = {
        playersCreated: 0,
        playersUpdated: 0,
        playersSkipped: 0,
        playersUnassigned: 0,
        usersCreated: 0,
        biddingsCreated: 0,
        biddingsUpdated: 0,
        transactionsCreated: 0
      };

      // Process each record
      for (let i = 0; i < records.length; i++) {
        const record = records[i];
        const isDebugRow = i === 0 || i % debugEveryNthRow === 0;
        
        if (isDebugRow) {
          console.log(`DEBUG [Row ${i}]: Processing record:`, record);
        }
        
        // Extract values ensuring we have fallbacks
        const playerName = record[playerNameIndex] || '';
        const belongsToColumn = record[belongsToIndex] || '';
        const teamName = record[0] || ''; // Team name is in the first column (index 0)
        
        // IMPORTANT: Log each player we're processing and the ownership status
        console.log(`Processing player [${i}]: ${playerName} - Team: ${teamName}, Owner: ${belongsToColumn || 'None'}`);
        
        // Use a safe approach for getting player type - different ways it might be stored
        let playerTypeStr = '';
        if (record[playerTypeIndex]) {
          playerTypeStr = record[playerTypeIndex];
        } else if (record.length > 4) {
          // Alternative way to get player type if indices are different
          const possibleTypes = ['BAT', 'BALL', 'WK', 'Batsman', 'Bowler', 'Wicket Keeper'];
          for (const type of possibleTypes) {
            if (record.includes(type)) {
              playerTypeStr = type;
              break;
            }
          }
        }
        
        // Check all user columns (columns after Base Price) to see if this player belongs to any of them
        let belongsTo = belongsToColumn; // Default to the "Belongs To" column value
        let highestBidAmount = null;
        let matchedPhoneNumber = null;
        let password = null;
        let ownerFound = false;
        
        // Look through all user columns (after Base Price) to find if this player has a bid from any user
        for (let j = basePriceIndex + 1; j < columnNames.length; j++) {
          // If this user column has a value for this player, they own the player
          if (record[j] && parseFloat(record[j]) > 0) {
            belongsTo = columnNames[j]; // Set the owner to this column name (user's name)
            highestBidAmount = parseFloat(record[j]); // Set the bid amount
            matchedPhoneNumber = phoneNumbers[j]; // Get the phone number from the 2nd row
            password = passwords[j]; // Get the password from the 3rd row
            ownerFound = true;
            
            if (isDebugRow) {
              console.log(`DEBUG [Row ${i}]: Player ${playerName} belongs to ${belongsTo} with bid ${highestBidAmount}`);
            }
            break; // Stop after finding the first owner
          }
        }
        
        // Safely get base price
        const basePrice = record[basePriceIndex] ? parseFloat(record[basePriceIndex]) : 0;
        
        // If no bid amount was found but player has an owner specified in "Belongs To" column, use base price as fallback
        if (!highestBidAmount && belongsTo && belongsTo.trim() !== '') {
          highestBidAmount = basePrice;
          console.log(`No bid amount found for ${playerName}, using base price: ${basePrice}`);
        }
        
        if (isDebugRow) {
          console.log(`DEBUG [Row ${i}]: Extracted data:`, {
            playerName,
            belongsToColumn,
            belongsTo: ownerFound ? belongsTo : 'Unassigned',
            playerTypeStr,
            basePrice,
            highestBidAmount
          });
        }
        
        // Skip empty rows
        if (!playerName && !belongsTo && !playerTypeStr) {
          if (isDebugRow) console.log(`DEBUG [Row ${i}]: Skipping empty row`);
          continue;
        }

        // Skip if playerName is missing
        if (!playerName) {
          console.warn(`Missing player name in row ${i}, skipping record:`, record);
          stats.playersSkipped++;
          continue;
        }

        // Log player status for debugging
        console.log(`Processing player: ${playerName}, hasOwner: ${belongsTo ? 'Yes' : 'No'}`);
        
        // Check if playerTypeStr is missing but try to use a default if possible
        if (!playerTypeStr) {
          console.warn(`Missing player type for ${playerName} in row ${i}, using default type`);
          // Instead of skipping, try to find a default type
          playerTypeStr = 'BAT'; // Use a default type
        }

        // Skip unsold players only if explicitly marked as "unsold" and not just empty
        if (belongsTo && belongsTo.toLowerCase() === 'unsold') {
          console.log(`Player marked as explicitly unsold: ${playerName} in row ${i}. Will create as unassigned.`);
          // Instead of skipping, we'll create the player but mark it as unassigned
          belongsTo = ''; // Clear the belongsTo so it's treated as unassigned
        }

        // Find or create player type
        let playerType = await PlayerType.findOne({
          where: { type: playerTypeStr }
        });

        // If player type doesn't exist, create a default one
        if (!playerType) {
          console.log(`Player type not found: ${playerTypeStr}, creating new type`);
          try {
            playerType = await PlayerType.create({
              type: playerTypeStr,
              created_at: new Date(),
              updated_at: new Date()
            });
          } catch (typeError) {
            console.error(`Error creating player type: ${typeError.message}`);
            // If we can't create the type, try to find an existing one to use
            playerType = await PlayerType.findOne();
            if (!playerType) {
              // If we still don't have a type, create a fallback
              try {
                playerType = await PlayerType.create({
                  type: 'BAT',
                  created_at: new Date(),
                  updated_at: new Date()
                });
              } catch (fallbackError) {
                console.error(`Failed to create fallback player type: ${fallbackError.message}`);
                stats.playersSkipped++;
                continue; // Skip this player if we can't create a type
              }
            }
          }
        }

        // Find or create team based on the team name in the first column
        let playerTeam;
        try {
          if (teamName && teamName.trim() !== '') {
            // Try to find the team by name
            playerTeam = await Team.findOne({
              where: { name: teamName }
            });

            // If team doesn't exist, create it
            if (!playerTeam) {
              console.log(`Team not found: ${teamName}, creating new team`);
              // Find the maximum ID value
              const maxIdResult = await sequelize.query(
                'SELECT COALESCE(MAX(id), 0) + 1 as next_id FROM teams',
                { type: sequelize.QueryTypes.SELECT }
              );
              const nextId = maxIdResult[0].next_id;

              // Create the team with explicit ID
              playerTeam = await Team.create({
                id: nextId,
                name: teamName,
                created_at: new Date(),
                updated_at: new Date()
              });
            }
          } else {
            // Fallback to any team if team name is not provided
            playerTeam = await Team.findOne();
            if (!playerTeam) {
              console.log('No team name provided and no teams found, creating a default team');
              playerTeam = await Team.create({
                name: 'Default Team',
                created_at: new Date(),
                updated_at: new Date()
              });
            }
          }
        } catch (teamError) {
          console.error(`Error with team ${teamName}: ${teamError.message}`);
          stats.playersSkipped++;
          continue; // Skip if we can't get a team
        }

        // Declare player variable in the outer scope so it's available throughout the loop
        let player;

        // Always create or update the player record, regardless of whether they have an owner
        try {
          player = await Player.findOne({
            where: { name: playerName },
            attributes: ['id', 'type_id', 'team_id'],
          });
          
          // Check if this is a new player or updating an existing one
          const isNewPlayer = !player;
          // Default to creating players with force_create_players (true by default if not specified)
          const forceCreate = force_create_players !== 'false'; // Default to true if not explicitly set to false
          const shouldCreate = isNewPlayer && (replacePlayers === 'true' || replaceAll === 'true' || forceCreate);
          const shouldUpdate = player && replacePlayers === 'true';
          
          // IMPORTANT DEBUG LOG - this will help us understand why players aren't being created
          console.log(`PLAYER ${playerName}: isNew=${isNewPlayer}, shouldCreate=${shouldCreate}, forceCreate=${forceCreate}, force_param=${force_create_players}`);
          
          if (isDebugRow) {
            console.log(`DEBUG [Row ${i}]: Player creation logic:`, {
              isNewPlayer,
              shouldCreate,
              shouldUpdate,
              forceCreate, 
              forceCreateFlag: force_create_players,
              replacePlayers: replacePlayers === 'true',
              replaceAll: replaceAll === 'true'
            });
          }
          
          // We should create players by default if they don't exist
          if (isNewPlayer) {
            if (shouldCreate) {
              // Create the player if it doesn't exist
              console.log(`Creating new player: ${playerName} with type ${playerTypeStr}`);
              player = await Player.create({
                name: playerName,
                type_id: playerType.id,
                base_price: basePrice,
                team_id: playerTeam.id,
                created_at: new Date(),
                updated_at: new Date()
              });
              stats.playersCreated++;
              
              // If this player doesn't have an owner, immediately mark as unassigned
              if (!belongsTo || belongsTo.trim() === '') {
                console.log(`New player ${playerName} has no owner, marking as unassigned`);
                stats.playersUnassigned++;
                continue;
              }
            } else {
              // Can't create the player because replaceAll, replacePlayers, and force_create_players are all false
              console.warn(`Player ${playerName} not found and creation options not enabled, skipping`);
              stats.playersSkipped++;
              continue;
            }
          } else if (shouldUpdate) {
            // Update the player if it exists and we're replacing players
            await Player.update({
              name: playerName,
              type_id: playerType.id,
              base_price: basePrice,
              team_id: playerTeam.id,
              updated_at: new Date()
            }, {
              where: { id: player.id }
            });
            stats.playersUpdated++;
          } else {
            // Existing player, not updating
            console.log(`Player ${playerName} already exists, not updating`);
            stats.playersUpdated++;
          }
        } catch (playerError) {
          console.error(`Error with player ${playerName}: ${playerError.message}`);
          stats.playersSkipped++;
          continue;
        }

        // If the player has no owner, mark as unassigned and continue to next player
        if (!belongsTo || belongsTo.trim() === '') {
          console.log(`Player ${playerName} has no owner, adding to available pool`);
          try {
            // Update the player to ensure it's properly tagged as unsold and available
            await Player.update(
              { 
                buy_price: null,  // No buy price since not sold
                updated_at: new Date()
              },
              { where: { id: player.id } }
            );
            
            // Check if there are any transactions for this player and remove them
            await Transaction.destroy({
              where: { player_id: player.id }
            });
            
            // Also remove any biddings for this player
            await Biddings.destroy({
              where: { player_id: player.id }
            });
            
            stats.playersUnassigned++;
            continue;
          } catch (error) {
            console.error(`Error marking player ${playerName} as unassigned: ${error.message}`);
            continue;
          }
        }
        
        const encryptedPassword = password ? await Crypt.hashPassword(password) : null;
        
        // Find buyer
        const buyer = await User.findOne({
          where: { name: belongsTo },
          attributes: ['id'],
        });

        if (isDebugRow) {
          console.log(`DEBUG [Row ${i}]: Looking for buyer ${belongsTo}, found: ${buyer ? 'Yes' : 'No'}`);
          if (matchedPhoneNumber) {
            console.log(`DEBUG [Row ${i}]: Phone number for ${belongsTo}: ${matchedPhoneNumber}`);
          }
        }

        if (!buyer) {
          console.log(`Buyer not found: ${belongsTo}, creating new user with phone: ${matchedPhoneNumber}`);
          try {
            const walletAmount = walletAmounts[columnNames.indexOf(belongsTo)] || 0;
            console.log(`Setting wallet amount for new user ${belongsTo}: ${walletAmount}`);
            
          const newUser = await User.create({
            name: belongsTo,
              remaining_balance: walletAmount,
              wallet_amount: walletAmount,
            role: 2,
            phone: matchedPhoneNumber,
            password: encryptedPassword,
          });
            stats.usersCreated++;
            console.log(`Created new user ${belongsTo} with ID ${newUser.id}`);

          // Create a new record in the Biddings table
          await Biddings.create({
            highest_bidder: newUser.id,
            player_id: player.id,
            highest_bid: highestBidAmount,
          });
            stats.biddingsCreated++;

          // Create a new record in the PlayerTransaction table
          await Transaction.create({
            buyer_id: newUser.id,
            player_id: player.id,
          });
            stats.transactionsCreated++;

            // Update player's buy price
          await Player.update(
            { buy_price: highestBidAmount },
            { where: { id: player.id } }
          );
          console.log(
              `New buyer ${newUser.name} (ID: ${newUser.id}) created and assigned player ${playerName} for ${highestBidAmount}`
          );
          } catch (userError) {
            console.error(`Error creating user ${belongsTo}: ${userError.message}`);
            stats.playersSkipped++;
            continue;
          }
        } else {
          console.log(`Buyer found: ${buyer.id}`);

          // Check if transaction already exists
          const existingTransaction = await Transaction.findOne({
            where: {
              player_id: player.id,
              buyer_id: buyer.id,
            },
          });

          if (!existingTransaction) {
            // Create a new transaction if it doesn't exist
            await Transaction.create({
              buyer_id: buyer.id,
              player_id: player.id,
            });
            stats.transactionsCreated++;
            console.log(`New transaction created for player ${player.id} by buyer ${buyer.id}`);
          } else {
            // Update the transaction if it exists
          await Transaction.update(
            { buyer_id: buyer.id },
            { where: { player_id: player.id } }
          );
            console.log(`Updated transaction for player ${player.id} to buyer ${buyer.id}`);
          }

          // Check if bidding already exists
          const existingBidding = await Biddings.findOne({
            where: {
              player_id: player.id,
              highest_bidder: buyer.id,
            },
          });

          if (existingBidding) {
            // Update existing bidding
            await Biddings.update(
              { highest_bid: highestBidAmount },
              {
                where: {
                  player_id: player.id,
                  highest_bidder: buyer.id,
                },
              }
            );
            stats.biddingsUpdated++;
            console.log(`Updated highest bid to ${highestBidAmount} for player ${player.id} by buyer ${buyer.id}`);
          } else {
            // Create new bidding
            await Biddings.create({
              highest_bidder: buyer.id,
              player_id: player.id,
              highest_bid: highestBidAmount,
            });
            stats.biddingsCreated++;
            console.log(`New bidding created for player ${player.id} by buyer ${buyer.id} with amount ${highestBidAmount}`);
          }

          // Update player's buy price
          await Player.update(
            { buy_price: highestBidAmount },
            { where: { id: player.id } }
          );
          console.log(`Updated buy price to ${highestBidAmount} for player ${playerName}`);
          
          // Update user's wallet amount if provided in the CSV
          const walletAmount = walletAmounts[columnNames.indexOf(belongsTo)];
          if (walletAmount && parseFloat(walletAmount) > 0) {
            await User.update(
              {
                remaining_balance: parseFloat(walletAmount),
                wallet_amount: parseFloat(walletAmount),
              },
              { where: { id: buyer.id } }
            );
            console.log(`Updated wallet amount for user ${belongsTo} to ${walletAmount}`);
          }
        }
      }

      // Process all users in the CSV to update their wallet balances
      console.log("Processing wallet amounts for all users...");
      for (let i = basePriceIndex + 1; i < columnNames.length; i++) {
        const userName = columnNames[i];
        const userWalletAmount = parseFloat(walletAmounts[i] || 0);
        const userPhone = phoneNumbers[i];
        const userPassword = passwords[i];
        
        if (userName && userName.trim() !== '') {
          console.log(`Processing wallet for user: ${userName}, amount: ${userWalletAmount}`);
          
          // Check if user already exists
          const existingUser = await User.findOne({
            where: { name: userName }
          });
          
          if (existingUser) {
            // Update existing user's wallet
        if (userWalletAmount > 0) {
              console.log(`Updating wallet for existing user ${userName} to ${userWalletAmount}`);
              await User.update(
            {
              remaining_balance: userWalletAmount,
              wallet_amount: userWalletAmount,
            },
            {
                  where: { id: existingUser.id }
                }
              );
            }
          } else if (userPhone) {
            // Create new user if they weren't created during player processing
            try {
              console.log(`Creating new user with name ${userName}, phone ${userPhone}`);
              const encryptedPassword = userPassword ? await Crypt.hashPassword(userPassword) : null;
              await User.create({
                name: userName,
                remaining_balance: userWalletAmount,
                wallet_amount: userWalletAmount,
                role: 2,
                phone: userPhone,
                password: encryptedPassword,
              });
              stats.usersCreated++;
            } catch (err) {
              console.error(`Error creating user ${userName}: ${err.message}`);
            }
          }
        }
      }

      // Prepare the final summary of the import
      const importSummary = {
        success: true,
        message: 'CSV data imported and database updated successfully',
        replacedData: replaceAll === 'true',
        replacedPlayers: replacePlayers === 'true',
        forceCreatePlayers: force_create_players === 'true',
        fileInfo: {
          filename: req.file.originalname,
          size: req.file.size,
          rows: records.length
        },
        stats,
        columnIndices: {
          playerNameIndex,
          belongsToIndex,
          playerTypeIndex,
          basePriceIndex
        },
        totalPlayersProcessed: stats.playersCreated + stats.playersUpdated + stats.playersSkipped
      };
      
      console.log('CSV data imported and database updated successfully.');
      return importSummary;
    } catch (error) {
      console.error('Error importing CSV data:', error);
      throw error;
    }
  }

  static async getAllUserDetails(req) {
    const playerTypeCounts = await Player.findAll({
      attributes: [
        'type_id',
        [sequelize.fn('COUNT', sequelize.col('player.id')), 'count'],
      ],
      include: [
        {
          model: PlayerType,
          attributes: ['type'],
        },
      ],
      group: ['type_id', 'player_type.id', 'player_type.type'],
      raw: true,
    });

    // Get users with their transactions
    const allUserDetails = await User.findAll({
      where: {
        role: 2,
      },
      include: [
        {
          model: Transaction,
          include: [
            {
              model: Player,
              include: [PlayerType, Team],
            },
          ],
        },
      ],
    });
    console.log(
      '🚀 ~ UserProfileService ~ getAllUserDetails ~ allUserDetails:',
      allUserDetails
    );

    const typeCountsObject = playerTypeCounts.reduce((acc, curr) => {
      const typeMapping = {
        BALL: 'totalBowler',
        WK: 'totalWK',
        BAT: 'totalBatsman',
      };

      const key =
        typeMapping[curr['player_type.type']] || curr['player_type.type'];
      acc[key] = parseInt(curr.count);
      return acc;
    }, {});

    const auctionData = await AuctionDetails.findOne({
      attributes: ['assigned_amount', 'max_batsman', 'max_bowler', 'max_wicketkeeper'],
      order: [['createdAt', 'DESC']],
    });
    
    const auctionSummary = auctionData ? auctionData.dataValues : {};
    console.log("🚀 ~ UserProfileService ~ allUserDetails.map ~ auctionInfo:", auctionSummary)
      const auctionInfo = {
                assigned_amount: auctionSummary.assigned_amount || 0,
                max_batsman: auctionSummary.max_batsman || 0,
                max_bowler: auctionSummary.max_bowler || 0,
                max_wicketkeeper: auctionSummary.max_wicketkeeper || 0
      }

    const sanitizedUsers = await Promise.all(
      allUserDetails.map(async (user) => {
        const userObj = user.dataValues;
        delete userObj.password;
        userObj.token = req.headers.authorization;

        // Count total purchased players
        const totalPurchased = await Transaction.count({
          where: { buyer_id: user.id },
        });

        // Count purchased bowlers
        const bowlers = await Transaction.count({
          include: [
            {
              model: Player,
              required: true,
              include: [
                {
                  model: PlayerType,
                  required: true,
                  where: { type: 'BALL' },
                },
              ],
            },
          ],
          where: { buyer_id: user.id },
        });

        // Count purchased batsmen
        const batsmen = await Transaction.count({
          include: [
            {
              model: Player,
              required: true,
              include: [
                {
                  model: PlayerType,
                  required: true,
                  where: { type: 'BAT' },
                },
              ],
            },
          ],
          where: { buyer_id: user.id },
        });

        // Count purchased wicket keepers
        const wicketKeepers = await Transaction.count({
          include: [
            {
              model: Player,
              required: true,
              include: [
                {
                  model: PlayerType,
                  required: true,
                  where: { type: 'WK' },
                },
              ],
            },
          ],
          where: { buyer_id: user.id },
        });

        const auctionData = await AuctionDetails.findOne({
          attributes: ['assigned_amount', 'max_batsman', 'max_bowler', 'max_wicketkeeper'],
          order: [['createdAt', 'DESC']],
        });
        
        const auctionInfo = auctionData ? auctionData.dataValues : {};
        console.log("🚀 ~ UserProfileService ~ allUserDetails.map ~ auctionInfo:", auctionInfo)

        // Add purchase summary
        userObj.purchaseSummary = {
          totalPurchased,
          bowlers,
          batsmen,
          wicketKeepers,
        };

        return userObj;
      })
    );

    return {
      users: sanitizedUsers,
      playerTypeCounts: typeCountsObject,
      auctionInfo: auctionInfo
    };
  }
}

module.exports = UserProfileService;