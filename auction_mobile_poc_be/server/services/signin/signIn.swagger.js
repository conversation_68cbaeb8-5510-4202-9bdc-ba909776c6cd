/**
 *  routes and schema for SignIn
 */

/**
 * @openapi
 * components:
 *  schemas:
 *      userSignIn:
 *          type: object
 *          required:
 *              - phone
 *              - password
 *          properties:
 *              phone:
 *                  type: string
 *                  description: user phone number
 *              password:
 *                  type: string
 *                  description: password for login
 *          example:
 *              phone: "9999999999"
 *              password: researcher
 */

/**
 * @openapi
 * /auth/signin:
 *  post:
 *      tags: [Authentication]
 *      summary: user signin
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/userSignIn'
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successLogin'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccess'
 *          401:
 *              description: User duplicate
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessLogin'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */
