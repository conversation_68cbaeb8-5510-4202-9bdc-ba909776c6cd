const crypt = require('../../util/crypt');
const SignInValidator = require('./signInValidator');
const User = require('../../models').user;

/**
 * Class represents services for signin.
 */
class SignInService {
    /**
     * @desc This function is being used to sign in user
     * <AUTHOR>
     * @since 22/09/2023
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {Object} req.body.phone email
     * @param {Object} req.body.password password
     * @param {Object} locale Locale passed from request
     * @param {Object} res Response
     */
    static async signIn (req, locale) {
        const Validator = new SignInValidator(req.body, locale);
        Validator.validate();
        return await SignInService.userLogin(req.body.phone, req.body.password);
    }

    /**
     * @desc This function is being used to end user login
     * <AUTHOR>
     * @since 22/09/2022
     * @param {Object} phone phone number
     * @param {Object} password password
     * @param {Object} res Response
     * @param {function} callback callback Handles Response data/error messages
     * @param {function} next exceptionHandler Calls exceptionHandler
     */
    static async userLogin (phone, password) {
        const user = await User.findOne({ where: { phone } });
        if (!user) {
            throw {
                message: MESSAGES.LOGIN_FAILED,
                statusCode: 401
            };
        }
        const isMatch = await crypt.comparePassword(password, user.password);

        if (!isMatch) {
            throw {
                message: MESSAGES.LOGIN_FAILED,
                statusCode: 401
            };
        } else {
            const token = await crypt.getUserAccessToken(user);
            let returnObj = user.dataValues;
            delete returnObj.password;
            returnObj = _.merge(returnObj, token);
            return returnObj;
        }
    }
}

module.exports = SignInService;
