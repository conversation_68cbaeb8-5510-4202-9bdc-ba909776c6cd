const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
const TestCase = require('./testcaseSignin');
chai.use(chaiHttp);
const trueDataStatus = 1;

describe('Signin Account', () => {
    try {
        TestCase.signinAccount.forEach((data) => {
            it(data.it, (done) => {
                request(process.env.BASE_URL)
                    .post('/auth/signin')
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.statusCode, 400);
                        done();
                    });
            });
        });

        it('As a user, I should validate if phone is not registered', (done) => {
            const loginUser = {
                'phone': '**********',
                'password': '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267d'
            };
            request(process.env.BASE_URL)
                .post('/auth/signin')
                .send(loginUser)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.body.status, 0);
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user, I should validate if invalid password', (done) => {
            const loginUser = {
                'phone': '9999999999',
                'password': '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267d'
            };
            request(process.env.BASE_URL)
                .post('/auth/signin')
                .send(loginUser)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.body.status, 0);
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user, I should validate and login with correct credentials', (done) => {
            const loginUser = {
                'phone': '9999999999',
                'password': 'Test@1234'
            };
            request(process.env.BASE_URL)
                .post('/auth/signin')
                .send(loginUser)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    expect(res.body.data.token).to.be.a('string');
                    assert.equal(res.body.status, trueDataStatus);
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });

        it('As a admin, I should validate and login', (done) => {
            const loginUser = {
                phone: '9876543210',
                password: 'Admin@1234'
            };
            request(process.env.BASE_URL)
                .post('/auth/signin')
                .send(loginUser)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    expect(res.body.data.token).to.be.a('string');
                    assert.equal(res.body.status, trueDataStatus);
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });

    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});
