const PointsService = require('./pointsService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for user Basic Profile.
 */
class PointsController {

  /**
* @desc This function is being used to sync player points
* <AUTHOR>
* @since 22/09/2023
* @param {Object} req Request
* @param {Object} req.body RequestBody
* @param {function} res Response
*/
  static async syncPlayerPoints (req, res) {
    try {
      const data = await PointsService.syncPlayerPoints(req.body, res.locals.user);
      Utils.sendResponse(null, data, res, res.__('SUCCESS'));
    } catch (error) {
      Utils.sendResponse(error, null, res, error.message);
    }
  }

  /**
* @desc This function is being used to get total points
* <AUTHOR>
* @since 22/09/2023
* @param {Object} req Request
* @param {Object} req.body RequestBody
* @param {function} res Response
*/
  static async getTotalPoints (req, res) {
    try {
      const data = await PointsService.getTotalPoints(req, res.locals.user);
      Utils.sendResponse(null, data, res, res.__('SUCCESS'));
    } catch (error) {
      Utils.sendResponse(error, null, res, error.message);
    }
  }
}

module.exports = PointsController;
