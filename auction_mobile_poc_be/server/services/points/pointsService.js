/* eslint-disable camelcase */
/* eslint-disable indent */
/* eslint-disable no-useless-catch */
const Points = require('../../models').points;
const axios = require('axios');
const apikey = process.env.CRICAPI_KEY;
const moment = require('moment');
const CONSTANTS = require('../../util/constants');
const { Sequelize } = require('../../models/index');

class PointsService {

  /**
* @desc This function is being used to syncPlayerPoints
* <AUTHOR>
* @since 22/09/2023
* @param {Object} req Request
* @param {Object} user User
*/
  static async syncPlayerPoints (req) {
    try {
      let updatingMatchesCount = 0;
      const result = await axios.get(`${CONSTANTS.CRICKET_API_ENDPOINT}/series_info?apikey=${apikey}&offset=0&id=${req.series_id}`);
      var data = result.data.data;
      CONSOLE_LOGGER.info(data, 'Data from result.data.data series_info');
      for (const eachSeriesInfo of data.matchList) {
        const match_type = eachSeriesInfo.matchType;
        const match_name = eachSeriesInfo.name;
        const status = eachSeriesInfo.status;
        const match_date = eachSeriesInfo.date;
        const existData = await this.isExistedMatchData(match_date, match_name);
        if (this.checkDate(match_date) &&
          updatingMatchesCount < 3 &&
          existData &&
          eachSeriesInfo.matchStarted === true &&
          eachSeriesInfo.matchEnded === true) {
          ++updatingMatchesCount;
          const result2 = await axios.
            get(`${CONSTANTS.CRICKET_API_ENDPOINT}/match_points?apikey=${apikey}&offset=0&id=${eachSeriesInfo.id}`);
          const pointsResult = result2.data.data;
          CONSOLE_LOGGER.info(pointsResult, 'pointsResult from result2.data.data match_points');
          if (pointsResult && pointsResult.totals) {
            for (const eachPlayerPoints of pointsResult.totals) {
              CONSOLE_LOGGER.info(eachPlayerPoints, 'eachPlayerPoints');
              const player_name = eachPlayerPoints.name;
              const player_recent_points = eachPlayerPoints.points;
              const result3 = await axios.
                get(`${CONSTANTS.CRICKET_API_ENDPOINT}/players_info?apikey=${apikey}&offset=0&id=${eachPlayerPoints.id}`);
              const player_team = result3.data.data.country;
              const match_status = status.includes(player_team) ? 'Won' : 'Lost';
              const final_result = {
                match_type, match_name, player_name, player_recent_points,
                match_status, player_team, match_date
              };
              await Points.create(final_result);
            }
          }
        }
      }
    } catch (error) {
      throw error;
    }
  }

  static checkDate (matchDate) {
    const now = moment().format('YYYY-MM-DD');
    const nowMoment = moment(now, 'YYYY-MM-DD');
    const matchDateMoment = moment(matchDate, 'YYYY-MM-DD');
    if (matchDateMoment.isBefore(nowMoment)) {
      return true;
    } else {
      return false;
    }
  }

  static async isExistedMatchData (match_date, match_name) {
    const result = await Points.findAndCountAll({
      where: {
        match_date, match_name
      }
    });
    if (result.count > 0) {
      return false;
    } else {
      return true;
    }
  }

  static async getTotalPoints (req, user) {
    const { playerOneTeam, playerTwoTeam, matchType } = req.body;
    let response;
    try {
      response = await Points.findAll({
        where: {
          match_type: matchType,
          [Sequelize.Op.or]: [
            { player_team: playerOneTeam },
            { player_team: playerTwoTeam }
          ]
        }
      });
    } catch (error) {
      CONSOLE_LOGGER.error('Database query failed:', error);
      throw error;
    }
    return response.map(eachResponse => eachResponse.toJSON());
  }

}

module.exports = PointsService;
