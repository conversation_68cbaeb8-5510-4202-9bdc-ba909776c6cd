/**
 *  routes and schema for Points
 */

/**
 * @openapi
 * /points/sync-player-points:
 *  post:
 *      security:
 *          - bearerAuth: []
 *      tags: [Points]
 *      summary: Points Sync
 *      requestBody:
 *        content:
 *          application/json:
 *            schema:
 *              type: object
 *              properties:
 *                series_id:
 *                  type: string
 *                  description: series_id of the match
 *                  example: "bd830e89-3420-4df5-854d-82cfab3e1e04"
 *      responses:
 *        200:
 *          description: success
 *          content:
 *            application/json:
 *              schema:
 *                $ref: '#/components/schemas/successLogin'
 *        400:
 *          description: Validation failed
 *          content:
 *            application/json:
 *              schema:
 *                $ref: '#/components/schemas/unauthorisedAccess'
 *        401:
 *          description: User duplicate
 *          content:
 *            application/json:
 *              schema:
 *                $ref: '#/components/schemas/unauthorisedAccessLogin'
 *        500:
 *          description: internal server error
 *          content:
 *            application/json:
 *              schema:
 *                $ref: '#/components/schemas/unexpectedError'
 *
 *
 * /points/total-points:
 *  post:
 *      security:
 *          - bearerAuth: []
 *      tags: [Points]
 *      summary: Total Points
 *      requestBody:
 *        content:
 *          application/json:
 *            schema:
 *              type: object
 *              properties:
 *                playerOneTeam:
 *                  type: string
 *                  description: player1 Team
 *                  example: "England"
 *                playerTwoTeam:
 *                  type: string
 *                  description: player2 Team
 *                  example: "Afghanistan"
 *                matchType:
 *                  type: string
 *                  description: matchType
 *                  example: "odi"
 *      responses:
 *        200:
 *          description: success
 *          content:
 *            application/json:
 *              schema:
 *                $ref: '#/components/schemas/successLogin'
 *        400:
 *          description: Validation failed
 *          content:
 *            application/json:
 *              schema:
 *                $ref: '#/components/schemas/unauthorisedAccess'
 *        401:
 *          description: User duplicate
 *          content:
 *            application/json:
 *              schema:
 *                $ref: '#/components/schemas/unauthorisedAccessLogin'
 *        500:
 *          description: internal server error
 *          content:
 *            application/json:
 *              schema:
 *                $ref: '#/components/schemas/unexpectedError'
 *
 */
