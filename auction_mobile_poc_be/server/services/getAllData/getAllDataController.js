const getAllDataService = require('./getAllDataService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for user Basic Profile.
 */
class getAllDataController {

    /**
     * @desc This function is being used to get all data when there is error in Socket
     * <AUTHOR>
     * @since 13/05/2025
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {function} res Response
     */
    static async getAllData (req, res) {
        try {
            const data = await getAllDataService.getData(req, res);
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = getAllDataController;
