/* eslint-disable camelcase */
/* eslint-disable indent */
/* eslint-disable no-useless-catch */

const Socket = require('../../util/socket');

class getAllDataService {
  /**
   * @desc This function is being used to get data of each event of socket when socket breaks or throws error
   * <AUTHOR>
   * @since 13/05/2025
   * @param {Object} req Request
   * @param {Object} res Response
   */
  static async getData(req) {
    let result;
    try {
      const { action, data } = req.body;

      if (!action) {
        throw new Error('Action is required');
      }

      switch (action) {
        case 'registerUser':
          result = await Socket.registerUser(null, null, data);
          break;

        case 'disconnectUser':
          result = await Socket.disconnectClient(null, null, data);
          break;

        case 'startAuction':
          await Socket.registerUser(null, null, data);
          result = await Socket.startAuction(null, null, data);
          break;

        case 'addBid':
          result = await Socket.addBid(null, null, data);
          break;

        case 'getNewPlayer':
          await Socket.registerUser(null, null, data);
          result = await Socket.getAnotherPlayer(null, null, data);
          break;

        case 'reconnect':
          result = await Socket.reConnect(null, null, data);
          break;

        case 'auctionStatus':
          result = await Socket.auctionStatus(null, null, data);
          break;

        case 'auctionCompleted':
          await Socket.registerUser(null, null, data);
          result = await Socket.auctionCompleted(null, null, data);
          break;

        case 'getUpcomingPlayers':
          result = await Socket.handleUpcomingPlayersRequest();
          break;

        case 'lastCall':
          await Socket.registerUser(null, null, data);
          result = await Socket.lastCall(null, null, data);
          break;

        case 'getUserDetails':
          await Socket.registerUser(null, null, data);
          result = await Socket.getUserDetails(null, null, data);
          break;

        case 'getAllUserDetails':
          await Socket.registerUser(null, null, data);
          result = await Socket.getAllUserDetails(null, null, data);
          break;

        case 'resetLastCallTimer':
          result = await Socket.resetLastCallTimer(null, null, data);
          break;

        case 'Success':
          result = await Socket.handleSuccess(null, null, data);
          break;

        case 'soldPlayer':
          result = await Socket.handleSoldPlayer(null, null, data);
          break;

        case 'playerUnsold':
          result = await Socket.handlePlayerUnsold(null, null, data);
          break;

        case 'liveBidders':
          result = await Socket.handleLiveBidders(null, null, data);
          break;

        default:
          throw new Error(`Invalid action: ${action}`);
      }

      return result;
    } catch (error) {
      console.error('getData error:', error.message);
      return { error: error.message };
    }
  }
}

module.exports = getAllDataService;
