/**
 * Contains security and schemas for error or success message definitions
 */

/**
 * @openapi
 * components:
 *   securitySchemes:
 *      bearerAuth:
 *          name: Authorization
 *          type: apiKey
 *          in: header
 *          bearerFormat: JWT
 *
 *   security:
 *      - bearerAuth: []
 *
 *   messageDefinition:
 *          type: object
 *          properties:
 *              status:
 *                  type: number
 *                  description: error code
 *              message:
 *                  type: string
 *                  description: error message
 *              data:
 *                  type: string
 *                  description: testing phone number
 *              phone:
 *                  type: string
 *                  description: unique phone number
 *              token:
 *                  type: string
 *                  description: token for signup and login
 *              password:
 *                 type: string
 *                 description: password for signup and login
 *              responseData:
 *                  type: object
 *
 *   schemas:
 *
 *      errorBadRequest:
 *          properties:
 *              status:
 *                 $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: Request is invalid
 *
 *      errorUserRegister:
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: This user is already registered with us.
 *
 *      unexpectedError:
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: Something went wrong. please try again.
 *
 *      agencySignUp:
 *          properties:
 *              email:
 *                  $ref: '#/components/messageDefinition/properties/email'
 *              password:
 *                  $ref: '#/components/messageDefinition/properties/password'
 *              token:
 *                  $ref: '#/components/messageDefinition/properties/token'
 *      successUserRegister:
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: User registration successful
 *
 *      successAgencyrRegister:
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Success
 *
 *      successAgencyruserEmail:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *              data:
 *                  $ref: '#/components/messageDefinition/properties/data'
 *          example:
 *              status: 1
 *              message: Success
 *              data: <EMAIL>
 *
 *      userVerify:
 *          type: object
 *          properties:
 *              email:
 *                  $ref: '#/components/messageDefinition/properties/email'
 *              otp:
 *                  $ref: '#/components/messageDefinition/properties/otp'
 *          example:
 *              email:
 *                  $ref: '#/components/schemas/userSignUp/example/email'
 *              otp: 123456
 *
 *      successVerifyUser:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Email is verified successfully
 *
 *      resendOTP:
 *          type: object
 *          properties:
 *              email:
 *                  $ref: '#/components/messageDefinition/properties/email'
 *          example:
 *              email:
 *                  $ref: '#/components/schemas/userSignUp/example/email'
 *      successResendOTP:
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Email resend successful
 *
 *      successLogin:
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *              data:
 *                  $ref: '#/components/messageDefinition/properties/responseData'
 *          example:
 *              status: 1
 *              message:
 *              data:
 *                  id: 1b3f59d1-6f84-4815-8e91-510e93e139c4
 *                  name: Jones
 *                  phone: "9999999999"
 *                  remaining_balance: 0
 *                  role: 2
 *                  token: TOKEN
 *                  createdAt: DATE
 *                  updatedAt: DATE
 *
 *      unauthorisedAccess:
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: Invalid user credentials.
 *
 *      unauthorisedAccessLogin:
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: Invalid user credentials.
 *
 *      userForgotPassword:
 *          type: object
 *          properties:
 *              email:
 *                  $ref: '#/components/messageDefinition/properties/email'
 *          example:
 *              email: <EMAIL>
 *
 *      userVerifyToken:
 *          type: object
 *          properties:
 *              token:
 *                  $ref: '#/components/messageDefinition/properties/token'
 *          example:
 *              token: 4hoR8EAXYEbT
 *              refreshToken: TOKEN
 *
 *      userResetPassword:
 *          type: object
 *          properties:
 *              token:
 *                  $ref: '#/components/messageDefinition/properties/token'
 *              password:
 *                  $ref: '#/components/messageDefinition/properties/password'
 *          example:
 *              token: 4hoR8EAXYEbT
 *              password: SHA256 encripted password
 *
 *      passwordInvalid:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: Please enter password.
 *
 *      errorForgotPassword:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: Please enter email address
 *
 *      successForgotPassword:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: An email has been sent. Please follow instructions on it.
 *
 *      successVerifyToken:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Link validated successfully.
 *
 *      errorVerifyToken:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: Link has expired, kindly reset password again.
 *
 *      successResetPassword:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Password updated successfully
 *
 *      successUserDetails:
 *          type: object
 *          properties:
 *              status:
 *                  type: integer
 *                  description: status if user is present
 *              data:
 *                  type: string
 *                  description: details of the user
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              data:
 *                  id: 1b3f59d1-6f84-4815-8e91-510e93e139c4
 *                  name: Jones
 *                  phone: "9999999999"
 *                  remaining_balance: 0
 *                  role: 2
 *                  token: TOKEN
 *                  createdAt: DATE
 *                  updatedAt: DATE
 *              message: Success
 *
 *      successUploadProfilePicture:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              data:
 *                  type: object
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *
 *          example:
 *              status: 1
 *              data:
 *                  profilePicture: s3url
 *              message: Success
 *
 *      successDeleteProfilePicture:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Success
 *
 *      changePassword:
 *          type: object
 *          properties:
 *              oldPassword:
 *                  type: string
 *              newPassword:
 *                  type: string
 *          example:
 *              oldPassword: OLD PASSWORD SHA256
 *              newPassword: NEW PASSWORD SHA256
 *
 *      successChangePassword:
 *          type: object
 *          properties:
 *              status:
 *                  type: boolean
 *                  description: status if user is present
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: true
 *              message: Password changed successfully
 *
 *      validationError:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: Request is invalid
 *
 *      unauthorisedAccessUser:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: You are not authorized to access this resource.
 *
 *
 */
