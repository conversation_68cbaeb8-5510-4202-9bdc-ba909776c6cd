/**
 * Database Controller
 */
const Utils = require('../../util/utilFunctions');
const dbService = require('./dbService');
const HTTPStatus = require('../../util/http-status');

/**
 * Reset database by truncating all tables
 * @param {Object} req Request
 * @param {Object} res Response
 */
const resetDatabase = async (req, res) => {
    try {
        const result = await dbService.resetDatabase();
        Utils.sendResponse(null, result, res, 'Database reset completed successfully');
    } catch (error) {
        CONSOLE_LOGGER.error('Error in resetDatabase controller:', error);
        Utils.sendResponse(error, null, res);
    }
};

module.exports = {
    resetDatabase
}; 