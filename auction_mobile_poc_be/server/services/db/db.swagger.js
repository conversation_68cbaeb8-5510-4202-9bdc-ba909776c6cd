/**
 *  Routes and schema for Database Management
 */

/**
 * @openapi
 * /db/reset:
 *   post:
 *     security:
 *       - bearerAuth: []
 *     tags: [Database]
 *     summary: Reset/clean the entire database
 *     description: Truncates all tables in the database (admin only)
 *     responses:
 *       200:
 *         description: Database reset completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Database reset completed successfully
 *       401:
 *         description: Unauthorized - not authenticated
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/unauthorisedAccessUser'
 *       403:
 *         description: Forbidden - not an admin
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Admin privileges required for this operation
 *       500:
 *         description: Server error during database reset
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/unexpectedError'
 */ 