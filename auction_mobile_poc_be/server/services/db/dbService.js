/**
 * Database Service
 */
const db = require('../../models');

/**
 * Clean/refresh the entire database by truncating all tables
 * This maintains the table structure but removes all data
 * @return {Object} Result of the operation
 */
const resetDatabase = async () => {
    const sequelize = db.sequelize;
    const dialect = sequelize.getDialect();
    console.log(`Database dialect detected: ${dialect}`);
    
    try {
        // Use transaction for safe operation
        const result = await sequelize.transaction(async (transaction) => {
            // Disable foreign key checks to avoid constraint issues during truncation
            // Use different command based on database type
            if (dialect === 'mysql') {
                await sequelize.query('SET FOREIGN_KEY_CHECKS = 0', { transaction });
            } else if (dialect === 'postgres') {
                await sequelize.query('SET session_replication_role = \'replica\'', { transaction });
            } else {
                console.log(`Warning: Unknown dialect ${dialect}, not disabling foreign key checks`);
            }
            
            // Get all model names
            const models = Object.keys(db).filter(key => {
                return key !== 'sequelize' && key !== 'Sequelize';
            });
            
            // Tables to exclude from truncation
            const excludeTables = ['user', 'player_type', 'teams'];
            
            // Truncate each table except those in excludeTables
            for (const modelName of models) {
                if (db[modelName].tableName && !excludeTables.includes(modelName)) {
                    // Different syntax for PostgreSQL and MySQL
                    let truncateQuery;
                    if (dialect === 'postgres') {
                        truncateQuery = `TRUNCATE TABLE "${db[modelName].tableName}" CASCADE`;
                    } else {
                        truncateQuery = `TRUNCATE TABLE \`${db[modelName].tableName}\``;
                    }
                    
                    await sequelize.query(truncateQuery, { transaction });
                    console.log(`Truncated table: ${db[modelName].tableName}`);
                } else if (excludeTables.includes(modelName)) {
                    console.log(`Skipped truncating table: ${db[modelName].tableName} (in exclusion list)`);
                }
            }
            
            // Re-enable foreign key checks
            if (dialect === 'mysql') {
                await sequelize.query('SET FOREIGN_KEY_CHECKS = 1', { transaction });
            } else if (dialect === 'postgres') {
                await sequelize.query('SET session_replication_role = \'origin\'', { transaction });
            }
            
            return { success: true, message: 'Database reset completed successfully (users, player types, and teams preserved)' };
        });
        
        return result;
    } catch (error) {
        console.error('Error resetting database:', error);
        throw new Error(`Failed to reset database: ${error.message}`);
    }
};

module.exports = {
    resetDatabase
}; 