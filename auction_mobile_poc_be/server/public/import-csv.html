<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Import CSV Data</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .container {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 20px;
            margin-top: 20px;
            border: 1px solid #ddd;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"] {
            border: 1px solid #ddd;
            padding: 5px;
            width: 100%;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        .checkbox-group {
            margin: 15px 0;
        }
        .response {
            margin-top: 20px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .warning {
            color: #856404;
            background-color: #fff3cd;
            border: 1px solid #ffeeba;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .indent {
            margin-left: 20px;
        }
    </style>
</head>
<body>
    <h1>Import CSV Data</h1>
    
    <div class="container">
        <div id="warning-box" class="warning">
            <strong>Warning:</strong> This import tool can modify your database in different ways:
            <ul>
                <li><strong>Standard Import:</strong> Will add/update bidding data for existing players.</li>
                <li><strong>Replace All Existing Data:</strong> Will clear all bidding data, transactions, player buy prices, and user wallet amounts before importing.</li>
                <li><strong>Replace Player Data:</strong> Will also replace or create player basic information (names, types, etc.).</li>
            </ul>
        </div>
        
        <form id="importForm" enctype="multipart/form-data">
            <div class="form-group">
                <label for="csvFile">Select CSV File:</label>
                <input type="file" id="csvFile" name="file" accept=".csv,.xlsx,.xls" required>
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" id="replaceAll" name="replaceAll" value="true">
                <label for="replaceAll">Replace all existing data</label>
                
                <div class="indent checkbox-group">
                    <input type="checkbox" id="replacePlayers" name="replacePlayers" value="true">
                    <label for="replacePlayers">Replace player data (create new players if missing)</label>
                </div>
                
                <div class="checkbox-group">
                    <input type="checkbox" id="forceCreatePlayers" name="force_create_players" value="true" checked>
                    <label for="forceCreatePlayers">Force create missing players (recommended)</label>
                </div>
            </div>
            
            <button type="submit">Import Data</button>
        </form>
        
        <div id="response" class="response">
            <h3>Response:</h3>
            <pre id="responseContent"></pre>
        </div>
    </div>

    <script>
        // Add dependency between checkboxes
        const replaceAllCheckbox = document.getElementById('replaceAll');
        const replacePlayersCheckbox = document.getElementById('replacePlayers');
        
        replaceAllCheckbox.addEventListener('change', function() {
            if (!this.checked) {
                // If "Replace All" is unchecked, the "Replace Players" checkbox can be independently checked
                replacePlayersCheckbox.disabled = false;
            } else {
                // Just visual indication that both are related
                replacePlayersCheckbox.disabled = false;
            }
        });
        
        document.getElementById('importForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(this);
            const replaceAllChecked = replaceAllCheckbox.checked;
            const replacePlayersChecked = replacePlayersCheckbox.checked;
            const forceCreatePlayersChecked = document.getElementById('forceCreatePlayers').checked;
            
            // Set form values based on checkboxes
            formData.set('replaceAll', replaceAllChecked ? 'true' : 'false');
            formData.set('replacePlayers', replacePlayersChecked ? 'true' : 'false');
            formData.set('force_create_players', forceCreatePlayersChecked ? 'true' : 'false');
            
            // Generate warning message based on options
            let warningMsg = "Are you sure you want to proceed with the import?";
            
            if (replaceAllChecked) {
                warningMsg = "WARNING: This will clear all existing bidding data, transactions, player buy prices, and user wallet amounts.";
                
                if (replacePlayersChecked) {
                    warningMsg += " It will also replace all player data.";
                }
                
                warningMsg += " Are you sure you want to continue?";
            } else if (replacePlayersChecked) {
                warningMsg = "WARNING: This will replace or create player basic information (names, types, etc.). Are you sure you want to continue?";
            }
            
            // Show confirmation
            if (!confirm(warningMsg)) {
                return;
            }
            
            try {
                // Get token from localStorage if available
                const token = localStorage.getItem('token') || prompt("Please enter your authentication token:");
                if (!token) return;
                
                // Store token for future use
                localStorage.setItem('token', token);
                
                // Show loading indicator
                const button = document.querySelector('button[type="submit"]');
                const originalText = button.textContent;
                button.textContent = 'Importing...';
                button.disabled = true;
                
                // Send request
                const response = await fetch('/user/import-csv', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'Authorization': token
                    }
                });
                
                const result = await response.json();
                
                // Display response
                const responseDiv = document.getElementById('response');
                const responseContent = document.getElementById('responseContent');
                responseDiv.style.display = 'block';
                responseContent.textContent = JSON.stringify(result, null, 2);
                
                // Show success or error styling
                if (response.ok) {
                    responseDiv.style.borderColor = '#4CAF50';
                    alert('Import completed successfully!');
                } else {
                    responseDiv.style.borderColor = '#f44336';
                    alert('Error importing data: ' + result.message || 'Unknown error');
                }
                
                // Restore button
                button.textContent = originalText;
                button.disabled = false;
            } catch (error) {
                console.error('Error:', error);
                alert('Error importing data: ' + error.message);
                
                // Restore button
                const button = document.querySelector('button[type="submit"]');
                button.textContent = 'Import Data';
                button.disabled = false;
            }
        });
    </script>
</body>
</html> 