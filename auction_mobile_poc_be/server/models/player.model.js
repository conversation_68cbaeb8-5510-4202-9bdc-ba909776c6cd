/**
 * @name player model
 * <AUTHOR>
 */
'use strict';
module.exports = function (sequelize, DataTypes) {
  const Player = sequelize.define('player', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    team_id: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    type_id: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    base_price: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    buy_price: DataTypes.INTEGER,
    status: {
      type: DataTypes.ENUM('bought', 'not-bought', 'unsold'),
      allowNull: false,
      defaultValue: 'not-bought'
    },
    createdAt: {
      type: DataTypes.DATE,
      default: Date.now
    },
    updatedAt: {
      type: DataTypes.DATE,
      default: Date.now
    }
  });

  Player.associate = (models) => {
    Player.hasOne(models.player_transaction, { foreignKey: 'player_id' });
    Player.hasMany(models.bidding, { foreignKey: 'player_id' });
    Player.belongsTo(models.player_type, { foreignKey: 'type_id' });
    Player.belongsTo(models.team, { foreignKey: 'team_id' });
  };

  return Player;
};
