/**
 * @name player_type model
 * <AUTHOR>
 */
'use strict';
module.exports = function (sequelize, DataTypes) {
    const PlayerType = sequelize.define('player_type', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true
        },
        type: {
            type: DataTypes.STRING,
            allowNull: false
        },
        createdAt: {
            type: DataTypes.DATE,
            default: Date.now
        },
        updatedAt: {
            type: DataTypes.DATE,
            default: Date.now
        }
    });

    PlayerType.associate = (models) => {
        PlayerType.hasMany(models.player, { foreignKey: 'type_id' });
    };

    return PlayerType;
};
