/**
 * @name bidding model
 * <AUTHOR>
 */
'use strict';
module.exports = function (sequelize, DataTypes) {
    const Bidding = sequelize.define('bidding', {
        id: {
            type: DataTypes.UUID,
            defaultValue: DataTypes.UUIDV4,
            primaryKey: true
        },
        player_id: {
            type: DataTypes.UUID,
            allowNull: false
        },
        highest_bidder: {
            type: DataTypes.UUID,
            allowNull: false
        },
        highest_bid: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        createdAt: {
            type: DataTypes.DATE,
            default: Date.now
        },
        updatedAt: {
            type: DataTypes.DATE,
            default: Date.now
        }
    });

    Bidding.associate = (models) => {
        Bidding.belongsTo(models.user, {
            foreignKey: 'highest_bidder'
        });
        Bidding.belongsTo(models.player, {
            foreignKey: 'player_id'
        });
    };

    return Bidding;
};
