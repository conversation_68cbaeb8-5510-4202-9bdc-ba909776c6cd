/**
 * @name user model
 * <AUTHOR>
 */
'use strict';
module.exports = function (sequelize, DataTypes) {
    const User = sequelize.define('user', {
        id: {
            type: DataTypes.UUID,
            defaultValue: DataTypes.UUIDV4,
            primaryKey: true
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false
        },
        phone: {
            type: DataTypes.STRING,
            unique: true,
            allowNull: false
        },
        password: DataTypes.STRING,
        wallet_amount: {
            type: DataTypes.INTEGER,
            defaultValue: 0
        },
        remaining_balance: {
            type: DataTypes.INTEGER,
            defaultValue: 0
        },
        bonus_balance:{
            type: DataTypes.INTEGER,
            defaultValue: 0
        },
        // 1 - admin, 2 - user
        role: {
            type: DataTypes.INTEGER,
            enum: [1, 2],
            default: 2
        },
        createdAt: {
            type: DataTypes.DATE,
            default: Date.now
        },
        updatedAt: {
            type: DataTypes.DATE,
            default: Date.now
        }
    });

    User.associate = (models) => {
        User.hasMany(models.player_transaction, { foreignKey: 'buyer_id' });
        User.hasMany(models.bidding, { foreignKey: 'highest_bidder' });
    };

    return User;
};
