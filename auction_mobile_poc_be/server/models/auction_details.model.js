/**
 * @name auction_details model
 * <AUTHOR>
 */
'use strict';
module.exports = function (sequelize, DataTypes) {
    return sequelize.define('auction_details', {
        id: {
            type: DataTypes.UUID,
            defaultValue: DataTypes.UUIDV4,
            primaryKey: true
        },
        max_batsman: {
            type: DataTypes.INTEGER,
            defaultValue: 0
        },
        max_bowler: {
            type: DataTypes.INTEGER,
            defaultValue: 0
        },
        max_wicketkeeper: {
            type: DataTypes.INTEGER,
            defaultValue: 0
        },
        assigned_amount: {
            type: DataTypes.INTEGER,
            defaultValue: 0
        },
        average_player_cost:{
            type: DataTypes.INTEGER,
            defaultValue: 0 
        },
        createdAt: {
            type: DataTypes.DATE,
            default: Date.now
        },
        updatedAt: {
            type: DataTypes.DATE,
            default: Date.now
        },
    },
    {
        timestamps: true
    });
};
