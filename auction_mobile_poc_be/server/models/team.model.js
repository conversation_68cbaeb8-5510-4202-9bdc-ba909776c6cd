/**
 * @name team model
 * <AUTHOR>
 */
'use strict';
module.exports = function (sequelize, DataTypes) {
    const Team = sequelize.define('team', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false
        },
        createdAt: {
            type: DataTypes.DATE,
            default: Date.now
        },
        updatedAt: {
            type: DataTypes.DATE,
            default: Date.now
        }
    });

    Team.associate = (models) => {
        Team.hasMany(models.player, { foreignKey: 'team_id' });
    };

    return Team;
};
