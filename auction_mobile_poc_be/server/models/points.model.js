/* eslint-disable indent */
/**
 * @name constant model
 * <AUTHOR>
 */
'use strict';
module.exports = function (sequelize, DataTypes) {
  return sequelize.define('point', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    match_type: {
      type: DataTypes.STRING,
      allowNull: false
    },
    match_name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    player_name: {
      allowNull: false,
      type: DataTypes.STRING
    },
    player_team: {
      type: DataTypes.STRING
    },
    player_recent_points: {
      type: DataTypes.INTEGER
    },
    match_date: {
      type: DataTypes.STRING
    },
    match_status: {
      allowNull: false,
      type: DataTypes.STRING
    },
    createdAt: {
      type: DataTypes.DATE,
      default: Date.now
    },
    updatedAt: {
      type: DataTypes.DATE,
      default: Date.now
    }
  });
};
