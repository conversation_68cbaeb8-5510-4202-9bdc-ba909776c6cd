/**
 * @name player_transaction model
 * <AUTHOR>
 */
'use strict';
module.exports = function (sequelize, DataTypes) {
    const PlayerTransaction = sequelize.define('player_transaction', {
        id: {
            type: DataTypes.UUID,
            defaultValue: DataTypes.UUIDV4,
            primaryKey: true
        },
        player_id: {
            type: DataTypes.UUID,
            allowNull: false
        },
        buyer_id: {
            type: DataTypes.STRING,
            allowNull: false
        },
        createdAt: {
            type: DataTypes.DATE,
            default: Date.now
        },
        updatedAt: {
            type: DataTypes.DATE,
            default: Date.now
        }
    });

    PlayerTransaction.associate = (models) => {
        PlayerTransaction.belongsTo(models.user, {
            foreignKey: 'buyer_id'
        });
        PlayerTransaction.belongsTo(models.player, {
            foreignKey: 'player_id'
        });
    };

    return PlayerTransaction;
};
