const jwt = require('jsonwebtoken');
const Utils = require('../util/utilFunctions');
const User = require('../models').user;
const HTTPStatus = require('../util/http-status');

/**
 * @desc This function is being used to authenticate each private request
 * <AUTHOR>
 * @since 01/03/2021
 * @param {Object} req Request req.headers RequestBody req.headers.accessToken accessToken
 * @param {Object} res Response
 * @param {function} next exceptionHandler Calls exceptionHandler
 */


const checkUser = (me, res, next) => {
    User.findOne({ where: { id: me.id } }).then((userObj) => {

        const responseObject = Utils.errorResponse();
        if (!userObj) {
            responseObject.message = res.__('ACCESS_DENIED');
            res.status(HTTPStatus.UNAUTHORIZED).send(responseObject);
            return;
        } else {
            // Do nothing
        }
        delete userObj.dataValues.password;

        res.locals.user = userObj;
        next();
    }).catch(next);
};


module.exports = function (req, res, next) {
    const token = req.headers.authorization;
    jwt.verify(token, process.env.JWT_ACCESS_SECRET, (err, tokenDetail) => {
        if (err) {
            const responseObject = Utils.errorResponse();
            responseObject.message = res.__('ACCESS_DENIED');
            res.status(HTTPStatus.UNAUTHORIZED).send(responseObject);
        } else {
            checkUser(tokenDetail, res, next);
            return;
        }
    });
};
