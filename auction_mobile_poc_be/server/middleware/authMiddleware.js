/**
 * Authentication Middleware
 */
const jwt = require('jsonwebtoken');
const db = require('../models');
const User = db.user;

/**
 * Middleware to authenticate JWT token
 */
const authenticateJWT = (req, res, next) => {
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
        return res.status(401).json({ 
            success: false, 
            message: 'Authentication token is required' 
        });
    }

    // Support both "Bearer token" and just "token" formats
    const token = authHeader.startsWith('Bearer ') ? authHeader.split(' ')[1] : authHeader;

    jwt.verify(token, process.env.JWT_ACCESS_SECRET, (err, user) => {
        if (err) {
            return res.status(401).json({ 
                success: false, 
                message: 'Invalid or expired token' 
            });
        }
        
        // Find user in database to ensure they still exist
        User.findOne({ where: { id: user.id } })
            .then(userObj => {
                if (!userObj) {
                    return res.status(401).json({ 
                        success: false, 
                        message: 'User not found' 
                    });
                }
                
                // Add user to BOTH request object and res.locals for compatibility
                req.user = userObj;
                res.locals.user = userObj;
                next();
            })
            .catch(error => {
                CONSOLE_LOGGER.error('Error in authenticateJWT middleware:', error);
                res.status(500).json({ 
                    success: false,
                    message: 'Error authenticating user' 
                });
            });
    });
};

/**
 * Middleware to check if user is an admin
 * Must be used after authenticateJWT middleware
 */
const isAdmin = (req, res, next) => {
    if (!req.user) {
        return res.status(401).json({ 
            success: false, 
            message: 'Authentication required' 
        });
    }
    
    // Check if user has admin role (using numeric value 1)
    if (req.user.role !== 1) {
        return res.status(403).json({ 
            success: false, 
            message: 'Admin privileges required for this operation' 
        });
    }
    
    next();
};

module.exports = {
    authenticateJWT,
    isAdmin
}; 