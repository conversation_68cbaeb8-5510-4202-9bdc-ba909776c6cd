const bcrypt = require('bcryptjs');
const JWT = require('./jwt');

class Crypt {
    /**
     * This function is being used to match the password with compare password
     * <AUTHOR>
     * @param {String} compare compare
     * @param {String} original original
     * @since 25/04/2023
     */
    static async comparePassword (compare, original) {
        return await bcrypt.compare(compare, original);
    }

    /**
     * This function is being used to match the password with encrypted password
     * <AUTHOR>
     * @param {String} compare compare
     * @param {String} original original
     * @since 25/04/2023
     */
    static async hashPassword (password) {
        const saltRounds = 10;
        return await bcrypt.hash(password, saltRounds);
    }

    /**
     * This function is being used to save user detail before login
     * <AUTHOR>
     * @param {Object} user user
     * @param {function} callback callback
     * @since 01/03/2021
     */
    static async getUserAccessToken (user) {
        const token = await JWT.generateAccessToken({
            id: user.id,
            phone: user.phone
        });

        return {
            token
        };
    }
}

module.exports = Crypt;
