const chai = require('chai');
const expect = chai.expect;
const assert = chai.assert;
const sinon = require('sinon');
const { sequelize } = require('../../models/index');
const User = require('../../models').user;
const Player = require('../../models').player;
const PlayerTransaction = require('../../models').player_transaction;
const Bidding = require('../../models').bidding;
const Socket = require('../socket');
const jwt = require('jsonwebtoken');
const MESSAGES = require('../../locales/en.json');
const { w3cwebsocket } = require('websocket');
const socket = require('../socket');

const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};
const expiredTokenOptionalInfo = {
    algorithm: 'HS256'
};

const userData = {
    id: 'fd604030-6fb5-11ec-8827-193cece4754a',
    email: '<EMAIL>'
};

const expiredUserData = {
    id: 'fd604030-6fb5-11ec-8827-193cece4754a',
    email: '<EMAIL>',
    exp: 1654606501
};

const validSocketToken = {
    token: jwt.sign(userData, process.env.JWT_ACCESS_SECRET, tokenOptionalInfo)
};
const expiredSocketToken = {
    token: jwt.sign(expiredUserData, process.env.JWT_ACCESS_SECRET, expiredTokenOptionalInfo)
};

describe('Socket', () => {
    describe('validateUser', () => {
        it('should return decoded JWT payload for a valid JWT', async () => {
            sinon.stub(User, 'findOne').resolves({ id: 'fd604030-6fb5-11ec-8827-193cece4754a' });
            const payload = {
                jwt: validSocketToken.token
            };
            const decodedPayload = await Socket.validateUser(payload);
            expect(decodedPayload.decodedJwtPayload.id).to.deep.equal('fd604030-6fb5-11ec-8827-193cece4754a');
            expect(User.findOne.calledOnce).to.be.true;
            User.findOne.restore();
        });

        it('should return decoded JWT payload for a valid admin user JWT', async () => {
            sinon.stub(User, 'findOne').resolves({ id: 'fd604030-6fb5-11ec-8827-193cece4754a', role: 1 });
            const payload = {
                jwt: validSocketToken.token
            };
            const decodedPayload = await Socket.validateUser(payload, true);
            expect(decodedPayload.decodedJwtPayload.id).to.deep.equal('fd604030-6fb5-11ec-8827-193cece4754a');
            expect(User.findOne.calledOnce).to.be.true;
            User.findOne.restore();
        });

        it('should return error for a user JWT', async () => {
            sinon.stub(User, 'findOne').resolves({ id: 'fd604030-6fb5-11ec-8827-193cece4754a', role: 2 });
            const payload = {
                jwt: validSocketToken.token
            };
            try {
                await Socket.validateUser(payload, true);
            } catch (error) {
                expect(error).to.have.property('message').to.equal(MESSAGES.UNAUTHORIZED);
            }
            User.findOne.restore();
        });

        it('should return error if user doesnt exists', async () => {
            sinon.stub(User, 'findOne').resolves(null);
            const payload = {
                jwt: validSocketToken.token
            };
            try {
                await Socket.validateUser(payload);
                expect.fail('Expected an error to be thrown for an invalid user');
            } catch (error) {
                expect(error).to.have.property('message').to.equal(MESSAGES.UNAUTHORIZED);
            }
            User.findOne.restore();

        });

        it('should throw an error for an expired JWT', async () => {
            const payload = {
                jwt: expiredSocketToken.token
            };
            try {
                await Socket.validateUser(payload);
                expect.fail('Expected an error to be thrown for an expired JWT');
            } catch (error) {
                expect(error).to.have.property('message').to.equal(MESSAGES.REGISTER_FAILED);
            }
        });

        it('should throw an error for an invalid user', async () => {
            sinon.stub(User, 'findOne').rejects(new Error(MESSAGES.INVALID_REQUEST));
            const payload = {
                jwt: validSocketToken.token
            };
            try {
                await Socket.validateUser(payload);
                expect.fail(MESSAGES.INVALID_REQUEST);
            } catch (error) {
                expect(error).to.have.property('message').to.equal(MESSAGES.INVALID_REQUEST);
                expect(User.findOne.calledOnce).to.be.true;
            }
            User.findOne.restore();
        });
    });

    describe('registerUser', () => {
        it('should register a user if not already registered', async () => {
            const ws = { id: 'some_socket_id' };
            const payload = {
                jwt: validSocketToken.token,
                action: 'register'
            };
            sinon.stub(Socket, 'validateUser').resolves({ id: 1, decodedJwtPayload: { id: 1 } });
            const sendResponseStub = sinon.stub(Socket, 'sendResponseToClient');
            await Socket.registerUser(ws, payload);
            expect(sendResponseStub.calledOnce).to.be.true;
            expect(sendResponseStub.firstCall.args[0]).to.equal(ws);
            expect(sendResponseStub.firstCall.args[1]).to.deep.equal({
                'socketId': ws.id,
                'status': true,
                'action': payload.action,
                'text': MESSAGES.AUTHORIZED
            });
            Socket.validateUser.restore();
            Socket.sendResponseToClient.restore();
        });

        it('should send a response for an already registered user', async () => {
            const ws = { id: 'some_socket_id' };
            const payload = {
                jwt: validSocketToken.token,
                action: 'register'
            };
            sinon.stub(Socket, 'validateUser').resolves({ id: 1 });
            const sendResponseStub = sinon.stub(Socket, 'sendResponseToClient');
            await Socket.registerUser(ws, payload);
            expect(sendResponseStub.calledOnce).to.be.true;
            expect(sendResponseStub.firstCall.args[0]).to.equal(ws);
            expect(sendResponseStub.firstCall.args[1]).to.deep.equal({
                'socketId': ws.id,
                'status': true,
                'action': payload.action,
                'text': MESSAGES.AUTHORIZED
            });
            Socket.validateUser.restore();
            Socket.sendResponseToClient.restore();
        });
    });

    describe('disconnectClient', () => {
        it('should disconnect client ', async () => {
            const ws = { id: 'some_socket_id', close: ()=>{} };
            const closeStub = sinon.stub(ws, 'close');
            await Socket.disconnectClient(ws);
            expect(closeStub.calledOnce).to.be.true;
        });
    });

    describe('broadcastMessage', () => {
        it('should broadcast a message to all connected clients', async () => {
            const ws = { id: 'some_socket_id' };
            const wsDisconnect = { id: 'some_socket_id', close: ()=>{} };
            const registerPayload = {
                jwt: validSocketToken.token,
                action: 'register'
            };
            const payload = {
                jwt: 'valid_jwt_here',
                action: 'broadcastMessage',
                text: 'Hello, world!'
            };
            const sendResponseStub = sinon.stub(Socket, 'sendResponseToClient');
            sinon.stub(Socket, 'validateUser').resolves({ id: 1, decodedJwtPayload: { id: 1 } });
            const connectedClients = [{ id: 'some_socket_id' }, { id: 'client2' }];
            await Socket.registerUser(ws, registerPayload);
            await Socket.broadcastMessage({ clients: connectedClients }, payload.action, payload.text);
            await Socket.disconnectClient(wsDisconnect);
            expect(sendResponseStub.called).to.be.true;
            Socket.sendResponseToClient.restore();
            Socket.validateUser.restore();
        });

        it('should broadcast a message to the user who bought player', async () => {
            const ws = { id: 'some_socket_id' };
            const wsDisconnect = { id: 'some_socket_id', close: ()=>{} };
            const registerPayload = {
                jwt: validSocketToken.token,
                action: 'register'
            };
            const payload = {
                jwt: 'valid_jwt_here',
                action: 'broadcastMessage',
                text: 'Hello, world!'
            };
            const sendResponseStub = sinon.stub(Socket, 'sendResponseToClient');
            sinon.stub(Socket, 'validateUser').resolves({ id: 1, decodedJwtPayload: { id: 1 } });
            const connectedClients = [{ id: 'some_socket_id' }, { id: 'client2' }];
            await Socket.registerUser(ws, registerPayload);
            await Socket.broadcastMessage({ clients: connectedClients }, payload.action, payload.text,
                { clientId: 'some_socket_id', player: { name: 'player', buy_price: 100 }, user: { name: 'test_user' } });
            await Socket.disconnectClient(wsDisconnect);
            expect(sendResponseStub.called).to.be.true;
            Socket.sendResponseToClient.restore();
            Socket.validateUser.restore();
        });

        it('should broadcast to all users except the user who bought', async () => {
            const ws = { id: 'some_socket_id' };
            const wsDisconnect = { id: 'some_socket_id', close: ()=>{} };
            const registerPayload = {
                jwt: validSocketToken.token,
                action: 'register'
            };
            const payload = {
                jwt: 'valid_jwt_here',
                action: 'broadcastMessage',
                text: 'Hello, world!'
            };
            const sendResponseStub = sinon.stub(Socket, 'sendResponseToClient');
            sinon.stub(Socket, 'validateUser').resolves({ id: 1, decodedJwtPayload: { id: 1 } });
            const connectedClients = [{ id: 'some_socket_id' }, { id: 'client2' }];
            await Socket.registerUser(ws, registerPayload);
            await Socket.broadcastMessage({ clients: connectedClients }, payload.action, payload.text,
                { clientId: 'client2', player: { name: 'player', buy_price: 100 }, user: { name: 'test_user' } });
            await Socket.disconnectClient(wsDisconnect);
            expect(sendResponseStub.called).to.be.true;
            Socket.sendResponseToClient.restore();
            Socket.validateUser.restore();
        });
    });

    describe('updatePlayerAsSold', () => {
        it('should update player as sold and deduct balance from user', async () => {
            const id = 123;
            const user = { id: 1, name: 'test-user' };
            const boughtPrice = 2000;

            sinon.stub(sequelize, 'transaction').returns({ commit: ()=> {}, rollback: ()=> {} });
            sinon.stub(User, 'update').resolves();
            sinon.stub(Player, 'update').resolves({ id: 1 });
            sinon.stub(PlayerTransaction, 'create').resolves();

            const player = await Socket.updatePlayerAsSold(id, user, boughtPrice);

            expect(player.id).to.equal(1);

            sequelize.transaction.restore();
            User.update.restore();
            Player.update.restore();
            PlayerTransaction.create.restore();
        });

        it('should rollback transaction if some error occurs', async () => {
            const id = 123;
            const user = { id: 1, name: 'test-user' };
            const boughtPrice = 2000;

            sinon.stub(sequelize, 'transaction').returns({ commit: ()=> {}, rollback: ()=> {} });
            sinon.stub(User, 'update').resolves();
            sinon.stub(Player, 'update').resolves({ id: 1 });
            sinon.stub(PlayerTransaction, 'create').rejects({ message: 'Error' });

            try {
                await Socket.updatePlayerAsSold(id, user, boughtPrice);
            } catch (error) {
                expect(error.message).to.equal('Error');
            }

            sequelize.transaction.restore();
            User.update.restore();
            Player.update.restore();
            PlayerTransaction.create.restore();
        });
    });

    describe('setUserWalletAmount', () => {
        it('should set balance amount of all users', async () => {
            const ws = { id: 'some_socket_id' };
            const wsDisconnect = { id: 'some_socket_id', close: ()=>{} };
            const registerPayload = {
                jwt: validSocketToken.token,
                action: 'register'
            };
            const payload = {
                jwt: 'valid_jwt_here',
                action: 'setUserWalletAmount',
                amount: 1000000
            };

            const sendResponseStub = sinon.stub(Socket, 'sendResponseToClient');
            sinon.stub(Socket, 'validateUser').resolves({ id: 1, decodedJwtPayload: { id: 1 } });
            sinon.stub(User, 'update').resolves();

            await Socket.registerUser(ws, registerPayload);
            await Socket.setUserWalletAmount(payload.amount);
            await Socket.disconnectClient(wsDisconnect);

            expect(sendResponseStub.calledOnce).to.be.true;

            Socket.sendResponseToClient.restore();
            Socket.validateUser.restore();
            User.update.restore();
        });
    });

    describe('setTimer', () => {
        it('should set timer between bids and set player unsold if no bids', async () => {
            const payload = {
                jwt: 'valid_jwt_here',
                action: 'setTimer',
                amount: 1000000
            };
            const connectedClients = [{ id: 'some_socket_id' }, { id: 'client2' }];
            const player = { id: 1 };

            sinon.stub(Player, 'update').resolves([1, [{ id: 'fd604030-6fb5-11ec-8827-193cece4754a' }]]);
            const broadcastMessageStub = sinon.stub(Socket, 'broadcastMessage').resolves();
            const clock = sinon.useFakeTimers();

            await Socket.setTimer(connectedClients, payload.action, player);
            await Socket.setTimer(connectedClients, payload.action, player);

            clock.tick(2000);
            expect(broadcastMessageStub.called).to.be.false;

            Player.update.restore();
            Socket.broadcastMessage.restore();
            clock.restore();
        });

        it('should set timer between bids and set player sold to highest bidder', async () => {
            const payload = {
                jwt: 'valid_jwt_here',
                action: 'setTimer',
                amount: 1000000
            };
            const connectedClients = [{ id: 'some_socket_id' }, { id: 'client2' }];
            const player = { id: 1 };

            sinon.stub(Socket, 'updatePlayerAsSold').resolves([1, [{ id: 'fd604030-6fb5-11ec-8827-193cece4754a' }]]);
            const broadcastMessageStub = sinon.stub(Socket, 'broadcastMessage').resolves();
            const clock = sinon.useFakeTimers();

            await Socket.setTimer(connectedClients, { id: 'some_socket_id' }, payload.action, player, 1000, { id: '123' });

            clock.tick(2000);
            expect(broadcastMessageStub.called).to.be.false;

            Socket.updatePlayerAsSold.restore();
            Socket.broadcastMessage.restore();
            clock.restore();
        });
    });

    describe('getAnotherPlayer without starting auction', () => {
        it('should send error message if getaAnotherPlayer is called before starting auction', async () => {
            const ws = { id: 'some_socket_id' };
            const wsDisconnect = { id: 'some_socket_id', close: ()=>{} };
            const registerPayload = {
                jwt: validSocketToken.token,
                action: 'register'
            };
            const payload = {
                jwt: validSocketToken.token,
                action: 'getNewPlayer',
                text: 'Hello, world!'
            };
            const sendResponseToClient = sinon.stub(Socket, 'sendResponseToClient');
            sinon.stub(Socket, 'validateUser').resolves({ id: 1, decodedJwtPayload: { id: 1 } });

            const connectedClients = [{ id: 'some_socket_id' }, { id: 'client2' }];
            await Socket.registerUser(ws, registerPayload);
            await Socket.getAnotherPlayer({ clients: connectedClients }, ws, payload);
            await Socket.disconnectClient(wsDisconnect);

            expect(sendResponseToClient.called).to.be.true;

            Socket.sendResponseToClient.restore();
            Socket.validateUser.restore();
        });
    });

    describe('getBasPriceAndMaxBid', () => {
        it('should get base price and max bid for player if there is not bid on player', async () => {
            sinon.stub(Bidding, 'findOne').resolves(null);

            const res = await Socket.getBasePriceAndMaxBid(1);
            expect(res).to.equal(null);

            Bidding.findOne.restore();
        });

        it('should get base price and max bid for player', async () => {
            sinon.stub(Bidding, 'findOne').resolves({ dataValues: { highest_bid: 100, highest_bidder: '123',
                player: { dataValues: { status: 'not-bought' } } } });

            const res = await Socket.getBasePriceAndMaxBid(1);
            expect(res.maxBid).to.equal(100);

            Bidding.findOne.restore();
        });

        it('handle if player is already bought or unsold', async () => {
            sinon.stub(Bidding, 'findOne').resolves({ dataValues: { highest_bid: 100, highest_bidder: '123',
                player: { dataValues: { status: 'unsold' } } } });

            try {
                expect(await Socket.getBasePriceAndMaxBid(1)).throws();
            } catch (error) {
                expect(error.message).to.equal('Request is invalid');
            }


            Bidding.findOne.restore();
        });
    });

    describe('addBiddingRecord', () => {
        it('should add bidding record to DB', async () => {
            sinon.stub(Bidding, 'create').resolves({ id: 123 });

            const res = await Socket.addBiddingRecord('123', 1000, '987');
            expect(res.id).to.equal(123);

            Bidding.create.restore();
        });
    });

    describe('checkCanUserBid', () => {
        it('return bidding record if user can bid and there are no bids currently', async () => {
            sinon.stub(Socket, 'addBiddingRecord').resolves({ id: 123 });

            const res = await Socket.checkCanUserBid(null, 1200, { dataValues:
                { base_price: 800 } }, { remaining_balance: 2000 });
            expect(res.id).to.equal(123);

            Socket.addBiddingRecord.restore();
        });

        it('return bidding record if user can bid', async () => {
            sinon.stub(Socket, 'addBiddingRecord').resolves({ id: 123 });

            const res = await Socket.checkCanUserBid({ maxBid: 1000, highestBidder: 456 }, 1200, { dataValues:
                { base_price: 800 } }, { remaining_balance: 2000, id: 987 });
            expect(res.id).to.equal(123);

            Socket.addBiddingRecord.restore();
        });

        it('should handle errors', async () => {
            sinon.stub(Socket, 'addBiddingRecord').resolves({ id: 123 });
            try {
                expect(await Socket.checkCanUserBid({ maxBid: 1000, highestBidder: 987 }, 1200, { dataValues:
                { base_price: 800 } }, { remaining_balance: 2000, id: 987 })).throws();
            } catch (error) {
                expect(error.message).to.equal('Request is invalid');
            }

            Socket.addBiddingRecord.restore();
        });
    });

    describe('addBid', () => {
        it('add bid and send reset timer event', async () => {
            const ws = { id: 'some_socket_id' };
            const payload = {
                jwt: 'valid_jwt_here',
                action: 'addBid',
                playerId: '456',
                bid: 2000
            };
            const wsDisconnect = { id: 'some_socket_id', close: ()=>{} };
            const registerPayload = {
                jwt: validSocketToken.token,
                action: 'register'
            };
            const connectedClients = [{ id: 'some_socket_id' }, { id: 'client2' }];

            sinon.stub(Socket, 'sendResponseToClient');
            sinon.stub(Socket, 'validateUser').resolves({ id: 1, decodedJwtPayload: { id: 1 }, user: { name: 'user' } });
            sinon.stub(Socket, 'getBasePriceAndMaxBid').resolves({ maxBid: 1000 });
            sinon.stub(Socket, 'checkCanUserBid').resolves({ id: '123' });
            sinon.stub(Socket, 'setTimer').resolves();
            const broadcastMessageStub = sinon.stub(Socket, 'broadcastMessage').resolves();
            sinon.stub(Player, 'findByPk').resolves({ name: 'test' });

            await Socket.registerUser(ws, registerPayload);
            await Socket.addBid({ clients: connectedClients }, ws, payload);
            await Socket.disconnectClient(wsDisconnect);

            expect(broadcastMessageStub.called).to.be.true;

            Socket.validateUser.restore();
            Socket.getBasePriceAndMaxBid.restore();
            Socket.checkCanUserBid.restore();
            Socket.setTimer.restore();
            Socket.broadcastMessage.restore();
            Socket.sendResponseToClient.restore();
            Player.findByPk.restore();
        });

        it('should send unauthorized response if the user is not registered', async () => {
            const ws = { id: 'some_socket_id' };
            const payload = {
                jwt: 'valid_jwt_here',
                action: 'addBid',
                playerId: '456',
                bid: 2000
            };
            const connectedClients = [{ id: 'some_socket_id' }, { id: 'client2' }];

            const sendResponseStub = sinon.stub(Socket, 'sendResponseToClient');
            sinon.stub(Socket, 'validateUser').resolves({ id: 1 });

            await Socket.addBid({ clients: connectedClients }, ws, payload);
            expect(sendResponseStub.called).to.be.true;

            Socket.sendResponseToClient.restore();
            Socket.validateUser.restore();
        });
    });

    describe('startAuction', () => {
        it('should start auction and broadcast to every user', async () => {
            const ws = { id: 'some_socket_id' };
            const wsDisconnect = { id: 'some_socket_id', close: ()=>{} };
            const registerPayload = {
                jwt: validSocketToken.token,
                action: 'register'
            };
            const payload = {
                jwt: validSocketToken.token,
                action: 'startAuction',
                text: 'Hello, world!'
            };
            const sendResponseStub = sinon.stub(Socket, 'sendResponseToClient');
            sinon.stub(Socket, 'validateUser').resolves({ id: 1, decodedJwtPayload: { id: 1 } });
            sinon.stub(Socket, 'setUserWalletAmount').resolves([1, [{ id: 'fd604030-6fb5-11ec-8827-193cece4754a' }]]);
            sinon.stub(Socket, 'setTimer').resolves();

            const connectedClients = [{ id: 'some_socket_id' }, { id: 'client2' }];
            await Socket.registerUser(ws, registerPayload);
            await Socket.startAuction({ clients: connectedClients }, ws, payload);
            await Socket.disconnectClient(wsDisconnect);

            expect(sendResponseStub.called).to.be.true;

            Socket.sendResponseToClient.restore();
            Socket.validateUser.restore();
            Socket.setUserWalletAmount.restore();
            Socket.setTimer.restore();
        });

        it('should send unauthorized response if the user is not registered', async () => {
            const ws = { id: 'some_socket_id' };
            const payload = {
                jwt: 'valid_jwt_here',
                action: 'startAuction',
                text: 'Hello, world!'
            };
            const sendResponseStub = sinon.stub(Socket, 'sendResponseToClient');
            sinon.stub(Socket, 'validateUser').resolves({ id: 1 });
            const connectedClients = [{ id: 'some_socket_id' }, { id: 'client2' }];
            await Socket.startAuction({ clients: connectedClients }, ws, payload);
            expect(sendResponseStub.called).to.be.true;
            Socket.sendResponseToClient.restore();
            Socket.validateUser.restore();
        });
    });

    describe('getAnotherPlayer', () => {
        it('should send unauthorized response if the user is not registered', async () => {
            const ws = { id: 'some_socket_id' };
            const payload = {
                jwt: 'valid_jwt_here',
                action: 'startAuction',
                text: 'Hello, world!'
            };
            const sendResponseStub = sinon.stub(Socket, 'sendResponseToClient');
            sinon.stub(Socket, 'validateUser').resolves({ id: 1 });

            const connectedClients = [{ id: 'some_socket_id' }, { id: 'client2' }];

            await Socket.getAnotherPlayer({ clients: connectedClients }, ws, payload);

            expect(sendResponseStub.called).to.be.true;
            Socket.sendResponseToClient.restore();
            Socket.validateUser.restore();
        });

        it('should send auction not started response if auction is not yet started', async () => {
            const ws = { id: 'some_socket_id' };
            const wsDisconnect = { id: 'some_socket_id', close: ()=>{} };
            const registerPayload = {
                jwt: validSocketToken.token,
                action: 'register'
            };
            const payload = {
                jwt: validSocketToken.token,
                action: 'getNewPlayer',
                text: 'Hello, world!'
            };
            sinon.stub(Socket, 'sendResponseToClient');
            sinon.stub(Socket, 'validateUser').resolves({ id: 1, decodedJwtPayload: { id: 1 } });
            sinon.stub(Socket, 'getPlayer').resolves({ id: 'fd604030-6fb5-11ec-8827-193cece4754a' });
            const brodcastMessageStub = sinon.stub(Socket, 'broadcastMessage').resolves();
            sinon.stub(Socket, 'setTimer').resolves();

            const connectedClients = [{ id: 'some_socket_id' }, { id: 'client2' }];
            await Socket.registerUser(ws, registerPayload);
            await Socket.getAnotherPlayer({ clients: connectedClients }, ws, payload);
            await Socket.disconnectClient(wsDisconnect);

            expect(brodcastMessageStub.calledOnce).to.be.true;

            Socket.sendResponseToClient.restore();
            Socket.validateUser.restore();
            Socket.getPlayer.restore();
            Socket.broadcastMessage.restore();
            Socket.setTimer.restore();
        });
    });

    describe('WebSocket Connection Handling', () => {
        let client;

        // eslint-disable-next-line no-undef
        beforeEach((done) => {
            client = new w3cwebsocket(process.env.SOCKET_BASE_URL);
            client.onopen = () => {
                done();
            };
        });

        // eslint-disable-next-line no-undef
        afterEach((done) => {
            client && client.close();
            done();
        });

        it('should handle registration of a new user', async () => {
            const messageBody = { action: 'registerUser', jwt: 'valid_jwt_here' };
            sinon.stub(socket, 'registerUser').resolves();
            client.send(JSON.stringify(messageBody));
            client.onmessage = (e) => {
                const message = JSON.parse(e.data);
                assert.equal(message.text, MESSAGES.AUTHORIZED);
            };
            socket.registerUser.restore();
        });

        it('should handle user disconnection', () => {
            const messageBody = { action: 'disconnectUser', jwt: 'valid_jwt_here' };
            sinon.stub(socket, 'disconnectClient').resolves();
            client.send(JSON.stringify(messageBody));
            client.onmessage = (e) => {
                const message = JSON.parse(e.data);
                assert.equal(message.text, MESSAGES.AUTHORIZED);
            };
            socket.disconnectClient.restore();
        });

        it('should start auction event', async () => {
            const messageBody = { action: 'startAuction', jwt: 'valid_jwt_here' };
            sinon.stub(socket, 'startAuction').resolves();
            client.send(JSON.stringify(messageBody));
            client.onmessage = (e) => {
                const message = JSON.parse(e.data);
                assert.equal(message.text, MESSAGES.AUTHORIZED);
            };
            socket.startAuction.restore();
        });

        it('should handle add bid event', async () => {
            const messageBody = { action: 'addBid', jwt: 'valid_jwt_here' };
            sinon.stub(socket, 'addBid').resolves();
            client.send(JSON.stringify(messageBody));
            client.onmessage = (e) => {
                const message = JSON.parse(e.data);
                assert.equal(message.text, MESSAGES.AUTHORIZED);
            };
            socket.addBid.restore();
        });

        it('should handle getNewPlayer event', async () => {
            const messageBody = { action: 'getNewPlayer', jwt: 'valid_jwt_here' };
            sinon.stub(socket, 'getAnotherPlayer').resolves();
            client.send(JSON.stringify(messageBody));
            client.onmessage = (e) => {
                const message = JSON.parse(e.data);
                assert.equal(message.text, MESSAGES.AUTHORIZED);
            };
            socket.getAnotherPlayer.restore();
        });

        it('should handle an invalid action', () => {
            const messageBody = { action: 'invalidAction', jwt: 'valid_jwt_here' };
            sinon.stub(socket, 'sendResponseToClient').resolves();
            client.send(JSON.stringify(messageBody));
            client.onmessage = (e) => {
                const message = JSON.parse(e.data);
                assert.equal(message.text, MESSAGES.AUTHORIZED);
            };
            socket.sendResponseToClient.restore();
        });
    });

});
