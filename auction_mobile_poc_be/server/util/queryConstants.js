module.exports = {
  EXPORT_CSV_QUERY: {
    players: `SELECT "user"."name",
                "user"."remaining_balance",
                "player_transactions->player"."name" AS "player_name",
                "player_transactions->player"."base_price" AS "base_price",
                "player_transactions->player"."buy_price" AS "purchase_price",
                "player_transactions->player->player_type"."type" AS "player_type",
                "player_transactions->player->team"."name" AS "team"
              FROM "users" AS "user"
              LEFT OUTER JOIN "player_transactions" AS "player_transactions" ON "user"."id" = "player_transactions"."buyer_id"
              LEFT OUTER JOIN "players" AS "player_transactions->player" ON "player_transactions"."player_id" = "player_transactions->player"."id"
              LEFT OUTER JOIN "player_types" AS "player_transactions->player->player_type" ON "player_transactions->player"."type_id" = "player_transactions->player->player_type"."id"
              LEFT OUTER JOIN "teams" AS "player_transactions->player->team" ON "player_transactions->player"."team_id" = "player_transactions->player->team"."id"
              WHERE ROLE = 2
              ORDER BY name ASC`,
    users: `SELECT "user"."name",
                "user"."remaining_balance",
                "player_transactions->player"."name" AS "player_name",
                "player_transactions->player"."base_price" AS "base_price",
                "player_transactions->player"."buy_price" AS "purchase_price",
                "player_transactions->player->player_type"."type" AS "player_type",
                "player_transactions->player->team"."name" AS "team"
              FROM "users" AS "user"
              LEFT OUTER JOIN "player_transactions" AS "player_transactions" ON "user"."id" = "player_transactions"."buyer_id"
              LEFT OUTER JOIN "players" AS "player_transactions->player" ON "player_transactions"."player_id" = "player_transactions->player"."id"
              LEFT OUTER JOIN "player_types" AS "player_transactions->player->player_type" ON "player_transactions->player"."type_id" = "player_transactions->player->player_type"."id"
              LEFT OUTER JOIN "teams" AS "player_transactions->player->team" ON "player_transactions->player"."team_id" = "player_transactions->player->team"."id"
              WHERE ROLE = 2
              ORDER BY name ASC`,
    getUser: `
              SELECT u.name 
              FROM users u
            `,
    userData: `SELECT 
                  u.name AS Name,
                  u.wallet_amount AS Funds_in_hand,
                  u.remaining_balance AS Balance,
                  CASE 
                      WHEN u.wallet_amount = 0 THEN 0 
                      ELSE u.wallet_amount - u.remaining_balance 
                  END AS Funds_used
              FROM users u;
              `,
    soldPlayer: (userColumns) => `
            WITH UserList AS (
              SELECT DISTINCT u.name, u.id
              FROM users u
              INNER JOIN player_transactions pt ON pt.buyer_id = u.id
            ),
            PlayerPurchases AS (
              SELECT 
                p.id,
                p.name AS player_name,
                t.name AS team_name,
                pt.type AS player_type,
                p.base_price,
                ptr.buyer_id,
                b.highest_bid,
                u.name AS buyer_name
              FROM players p
              LEFT JOIN teams t ON p.team_id = t.id
              LEFT JOIN player_types pt ON p.type_id = pt.id
              LEFT JOIN player_transactions ptr ON p.id = ptr.player_id
              LEFT JOIN users u ON ptr.buyer_id = u.id
              LEFT JOIN biddings b ON p.id = b.player_id AND b.highest_bidder = ptr.buyer_id
            )
            SELECT 
              pb.team_name AS Team,
              pb.player_name AS "Players Name",
              COALESCE(pb.buyer_name, 'Unsold') AS "Belongs To",
              pb.team_name AS "Team",
              pb.player_type AS "TYPE",
              pb.base_price AS "Base Price",
              ${userColumns}
            FROM PlayerPurchases pb
            CROSS JOIN UserList ul
            GROUP BY 
              pb.team_name,
              pb.player_name,
              pb.buyer_name,
              pb.player_type,
              pb.base_price
            ORDER BY pb.team_name, pb.player_name;
          `,
  },
};
