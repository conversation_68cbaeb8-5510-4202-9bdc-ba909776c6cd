const GeneralError = require('../util/GeneralError');
const REQUIRED = 'FIELD_REQUIRED';
const INVALID = 'FIELD_NOT_VALID';

/**
 * Created by Growexx on 04/06/2020
 * @name alidator
 */
class Validator {
    constructor (locale) {
        this.NOT_VALID = INVALID;
        this.REQUIRED = REQUIRED;

        if (locale) {
            this.__ = locale;
        }
    }

    /**
     * @desc This function is being used to check password
     * <AUTHOR>
     * @since 01/03/2021
     * @param {string} password Password
     */
    password (password) {
        if (!password) {
            throw new GeneralError(this.__(REQUIRED, 'Password'), 400);
        }
    }

    /**
     * @desc This function is being used to check phone number
     * <AUTHOR>
     * @since 22/09/2023
     * @param {string} phone phone number
     */
    phone (phone) {
        if (!phone) {
            throw new GeneralError(this.__(REQUIRED, 'Phone number'), 400);
        }
    }
}

module.exports = Validator;
