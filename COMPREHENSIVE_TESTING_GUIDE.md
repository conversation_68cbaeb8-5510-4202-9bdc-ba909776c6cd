# 🎯 Comprehensive Multi-Auction Testing Guide

## **Server Configuration**

- **Server URL**: `http://localhost:3006`
- **Admin JWT**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.L5bwOIYuxOht21r55VFEhh-p3J8wSCER57yksAqQluM`

---

## **🔄 COMPLETE TESTING FLOW**

### **📋 Prerequisites**

1. **Start Server**: `npm start` (runs on port 3006)
2. **Use Socket.IO Client**: Postman WebSocket or browser console
3. **Connect to**: `ws://localhost:3006`

---

## **🎯 FLOW 1: Admin Registration & Auction Creation**

### **Step 1: Admin Registration (No auctionId required)**

```json
{
  "action": "registerAdmin",
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.L5bwOIYuxOht21r55VFEhh-p3J8wSCER57yksAqQluM"
}
```

**Expected Response:**

```json
{
  "socketId": "socket_id",
  "status": true,
  "action": "registerAdmin",
  "text": "Authorized",
  "user": {
    "id": "bfe5096f-9ad2-40ae-aef4-d38d9625b6d0",
    "role": 1,
    "name": "Admin Name"
  }
}
```

### **Step 2: Create Auction**

```json
{
  "action": "createAuction",
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.L5bwOIYuxOht21r55VFEhh-p3J8wSCER57yksAqQluM",
  "name": "IPL 2024 Test Auction",
  "description": "Comprehensive test auction for IPL 2024",
  "startDate": "2024-01-15T10:00:00Z",
  "endDate": "2024-01-15T18:00:00Z"
}
```

**Expected Response:**

```json
{
  "status": true,
  "auction": {
    "id": "generated-uuid-here",
    "name": "IPL 2024 Test Auction",
    "description": "Comprehensive test auction for IPL 2024",
    "status": "inactive",
    "created_by": "bfe5096f-9ad2-40ae-aef4-d38d9625b6d0",
    "start_time": "2024-01-15T10:00:00.000Z",
    "end_time": "2024-01-15T18:00:00.000Z"
  }
}
```

**📝 Save the `auction.id` for next steps!**

### **Step 3: Get All Auctions**

```json
{
  "action": "getAuctions",
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.L5bwOIYuxOht21r55VFEhh-p3J8wSCER57yksAqQluM"
}
```

### **Step 4: Join Auction (Admin)**

```json
{
  "action": "joinAuction",
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.L5bwOIYuxOht21r55VFEhh-p3J8wSCER57yksAqQluM",
  "auctionId": "generated-uuid-here"
}
```

---

## **🎯 FLOW 2: Start Auction & Bidding**

### **Step 5: Start Auction (Admin)**

```json
{
  "action": "startAuction",
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.L5bwOIYuxOht21r55VFEhh-p3J8wSCER57yksAqQluM",
  "auctionId": "generated-uuid-here",
  "duration": 60,
  "averagePlayerCost": 100000,
  "batter": 2,
  "bowler": 2,
  "wicketKeeper": 1,
  "batterMax": 2,
  "bowlerMax": 2,
  "wicketKeeperMax": 1,
  "amount": 1000000
}
```

### **Step 6: Get New Player**

```json
{
  "action": "getNewPlayer",
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.L5bwOIYuxOht21r55VFEhh-p3J8wSCER57yksAqQluM",
  "auctionId": "generated-uuid-here"
}
```

**📝 Save the `player.id` from response for bidding!**

### **Step 7: Add First Bid**

```json
{
  "action": "addBid",
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.L5bwOIYuxOht21r55VFEhh-p3J8wSCER57yksAqQluM",
  "auctionId": "generated-uuid-here",
  "playerId": "player-id-from-getNewPlayer",
  "bid": 50000
}
```

---

## **🎯 FLOW 3: Auction Status & Monitoring**

### **Step 8: Get Auction Status**

```json
{
  "action": "auctionStatus",
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.L5bwOIYuxOht21r55VFEhh-p3J8wSCER57yksAqQluM",
  "auctionId": "generated-uuid-here"
}
```

### **Step 9: Get Live Bidders**

```json
{
  "action": "liveBidders",
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.L5bwOIYuxOht21r55VFEhh-p3J8wSCER57yksAqQluM",
  "auctionId": "generated-uuid-here"
}
```

### **Step 10: Add Higher Bid**

```json
{
  "action": "addBid",
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.L5bwOIYuxOht21r55VFEhh-p3J8wSCER57yksAqQluM",
  "auctionId": "generated-uuid-here",
  "playerId": "player-id-from-getNewPlayer",
  "bid": 75000
}
```

---

## **🎯 FLOW 4: Last Call & Player Completion**

### **Step 11: Get Upcoming Players**

```json
{
  "action": "getUpcomingPlayers",
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.L5bwOIYuxOht21r55VFEhh-p3J8wSCER57yksAqQluM",
  "auctionId": "generated-uuid-here"
}
```

### **Step 12: Last Call (Admin)**

```json
{
  "action": "lastCall",
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.L5bwOIYuxOht21r55VFEhh-p3J8wSCER57yksAqQluM",
  "auctionId": "generated-uuid-here",
  "playerId": "player-id-from-getNewPlayer"
}
```

**⏰ Wait for last call countdown to complete**

### **Step 13: Add Final Bid (if needed)**

```json
{
  "action": "addBid",
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.L5bwOIYuxOht21r55VFEhh-p3J8wSCER57yksAqQluM",
  "auctionId": "generated-uuid-here",
  "playerId": "player-id-from-getNewPlayer",
  "bid": 100000
}
```

### **Step 14: Auction Completed (Admin)**

```json
{
  "action": "auctionCompleted",
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.L5bwOIYuxOht21r55VFEhh-p3J8wSCER57yksAqQluM",
  "auctionId": "generated-uuid-here",
  "playerId": "player-id-from-getNewPlayer"
}
```

---

## **🎯 FLOW 5: User Management & Details**

### **Step 15: Get All User Details**

```json
{
  "action": "getAllUserDetails",
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.L5bwOIYuxOht21r55VFEhh-p3J8wSCER57yksAqQluM",
  "auctionId": "generated-uuid-here"
}
```

### **Step 16: Get Auction Details**

```json
{
  "action": "getAuctionDetails",
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.L5bwOIYuxOht21r55VFEhh-p3J8wSCER57yksAqQluM",
  "id": "generated-uuid-here"
}
```

---

## **🎯 FLOW 6: Auction Management**

### **Step 17: Update Auction**

```json
{
  "action": "updateAuction",
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.L5bwOIYuxOht21r55VFEhh-p3J8wSCER57yksAqQluM",
  "id": "generated-uuid-here",
  "data": {
    "status": "completed",
    "description": "Updated description after completion"
  }
}
```

### **Step 18: System Health**

```json
{
  "action": "systemHealth"
}
```

---

## **🎯 FLOW 7: Multi-Auction Testing**

### **Step 19: Create Second Auction**

```json
{
  "action": "createAuction",
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.L5bwOIYuxOht21r55VFEhh-p3J8wSCER57yksAqQluM",
  "name": "Second Test Auction",
  "description": "Testing multi-auction isolation",
  "startDate": "2024-01-16T10:00:00Z",
  "endDate": "2024-01-16T18:00:00Z"
}
```

### **Step 20: Verify Auction Isolation**

```json
{
  "action": "getAuctions",
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.L5bwOIYuxOht21r55VFEhh-p3J8wSCER57yksAqQluM"
}
```

---

## **🎯 FLOW 8: Error Testing**

### **Step 21: Test Invalid auctionId**

```json
{
  "action": "registerUser",
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.L5bwOIYuxOht21r55VFEhh-p3J8wSCER57yksAqQluM",
  "auctionId": "invalid-auction-id"
}
```

**Expected Response:**

```json
{
  "action": "error",
  "message": "auctionId is required for user registration",
  "statusCode": 400
}
```

### **Step 22: Test Missing auctionId**

```json
{
  "action": "registerUser",
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.L5bwOIYuxOht21r55VFEhh-p3J8wSCER57yksAqQluM"
}
```

**Expected Response:**

```json
{
  "action": "error",
  "message": "auctionId is required for user registration",
  "statusCode": 400
}
```

### **Step 23: Test Non-Admin Create Auction**

```json
{
  "action": "createAuction",
  "jwt": "user-jwt-token-here",
  "name": "Should Fail",
  "description": "This should fail"
}
```

**Expected Response:**

```json
{
  "action": "error",
  "message": "Unauthorized",
  "statusCode": 400
}
```

---

## **🎯 FLOW 9: User Registration & Participation**

### **Step 24: User Registration (Different JWT)**

```json
{
  "action": "registerUser",
  "jwt": "user-jwt-token-here",
  "auctionId": "generated-uuid-here"
}
```

### **Step 25: User Join Auction**

```json
{
  "action": "joinAuction",
  "jwt": "user-jwt-token-here",
  "auctionId": "generated-uuid-here"
}
```

### **Step 26: User Add Bid**

```json
{
  "action": "addBid",
  "jwt": "user-jwt-token-here",
  "auctionId": "generated-uuid-here",
  "playerId": "player-id-from-getNewPlayer",
  "bid": 85000
}
```

---

## **🎯 FLOW 10: Cleanup**

### **Step 27: Disconnect User**

```json
{
  "action": "disconnectUser",
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.L5bwOIYuxOht21r55VFEhh-p3J8wSCER57yksAqQluM",
  "auctionId": "generated-uuid-here"
}
```

---

## **📋 Testing Checklist**

### **✅ Admin Flow:**

- [ ] Admin registration (no auctionId)
- [ ] Create auction
- [ ] Auto-registration to created auction
- [ ] Start auction
- [ ] Manage auction

### **✅ User Flow:**

- [ ] User registration (with auctionId)
- [ ] Join auction
- [ ] Place bids
- [ ] View auction status

### **✅ Auction Events:**

- [ ] Get new player
- [ ] Add bid
- [ ] Last call
- [ ] Auction completed
- [ ] Get upcoming players

### **✅ Multi-Auction:**

- [ ] Create multiple auctions
- [ ] Verify isolation between auctions
- [ ] Test state separation

### **✅ Error Handling:**

- [ ] Invalid auctionId
- [ ] Missing auctionId
- [ ] Non-admin permissions
- [ ] Invalid JWT

---

## **🚀 Quick Test Commands**

### **For Postman WebSocket:**

1. Connect to `ws://localhost:3006`
2. Send each JSON payload in sequence
3. Check responses for success/error

### **For Browser Console:**

```javascript
// Connect to socket
const socket = io('http://localhost:3006');

// Send events
socket.emit('registerAdmin', { jwt: 'your-jwt' });
socket.emit('createAuction', { jwt: 'your-jwt', name: 'Test', ... });

// Listen for responses
socket.on('registerAdmin', (data) => console.log(data));
socket.on('createAuction', (data) => console.log(data));
```

---

## **🎯 Key Features Verified**

1. **✅ Admin Registration**: No auctionId required
2. **✅ Auction Creation**: Proper field mapping
3. **✅ Multi-Auction Support**: State isolation
4. **✅ Real-time Bidding**: Socket-based communication
5. **✅ Last Call System**: Countdown mechanism
6. **✅ Player Management**: Get new players, upcoming players
7. **✅ User Management**: Registration, joining, disconnection
8. **✅ Error Handling**: Proper validation and error messages
9. **✅ Auction Lifecycle**: Start → Bid → Last Call → Complete
10. **✅ State Management**: Auction-aware state isolation

---

## **🔧 Troubleshooting**

### **Common Issues:**

1. **Connection Failed**: Check if server is running on port 3006
2. **JWT Expired**: Generate new JWT token
3. **Invalid auctionId**: Use valid UUID from createAuction response
4. **Missing auctionId**: Required for user registration
5. **Admin Permission**: Use admin JWT for admin-only actions

### **Expected Success Rate: 100%** 🎯

This comprehensive testing guide covers all aspects of your multi-auction system!
